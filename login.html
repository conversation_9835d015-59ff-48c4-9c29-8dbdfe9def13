<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - EG BANK</title>
    <link rel="stylesheet" href="login-styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="login-container">
        <div class="login-background">
            <div class="shape shape1"></div>
            <div class="shape shape2"></div>
            <div class="shape shape3"></div>
        </div>
        
        <div class="login-card">
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-shopping-cart"></i>
                    <span>EG BANK</span>
                </div>
                <h2>تسجيل الدخول</h2>
                <p>أدخل بياناتك للوصول إلى المتجر</p>
            </div>
            
            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="username">اسم المستخدم</label>
                    <div class="input-container">
                        <i class="fas fa-user"></i>
                        <input type="text" id="username" name="username" placeholder="أدخل اسم المستخدم" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <div class="input-container">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="password" name="password" placeholder="أدخل كلمة المرور" required>
                        <button type="button" class="toggle-password" onclick="togglePassword()">
                            <i class="fas fa-eye" id="toggleIcon"></i>
                        </button>
                    </div>
                </div>
                
                <div class="form-options">
                    <label class="remember-me">
                        <input type="checkbox" id="rememberMe">
                        <span class="checkmark"></span>
                        تذكرني
                    </label>
                    <a href="#" class="forgot-password" onclick="showForgotPassword()">نسيت كلمة المرور؟</a>
                </div>
                
                <button type="submit" class="login-btn">
                    <span class="btn-text">تسجيل الدخول</span>
                    <i class="fas fa-arrow-right btn-icon"></i>
                </button>
                
                <div class="divider">
                    <span>أو</span>
                </div>
                
                <button type="button" class="register-btn" onclick="showRegisterForm()">
                    <i class="fas fa-user-plus"></i>
                    إنشاء حساب جديد
                </button>
            </form>
            
            <div class="demo-accounts">
                <h4>حسابات تجريبية:</h4>
                <div class="demo-list">
                    <div class="demo-item" onclick="fillDemoAccount('admin', 'admin123')">
                        <strong>المدير:</strong> admin / admin123
                    </div>
                    <div class="demo-item" onclick="fillDemoAccount('user', 'user123')">
                        <strong>مستخدم:</strong> user / user123
                    </div>
                    <div class="demo-item" onclick="fillDemoAccount('guest', 'guest123')">
                        <strong>ضيف:</strong> guest / guest123
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Register Modal -->
        <div id="registerModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>إنشاء حساب جديد</h3>
                    <span class="close" onclick="closeRegisterModal()">&times;</span>
                </div>
                <form id="registerForm" class="register-form">
                    <div class="form-group">
                        <label for="newUsername">اسم المستخدم</label>
                        <div class="input-container">
                            <i class="fas fa-user"></i>
                            <input type="text" id="newUsername" name="newUsername" placeholder="اختر اسم مستخدم" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">البريد الإلكتروني</label>
                        <div class="input-container">
                            <i class="fas fa-envelope"></i>
                            <input type="email" id="email" name="email" placeholder="أدخل بريدك الإلكتروني" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="newPassword">كلمة المرور</label>
                        <div class="input-container">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="newPassword" name="newPassword" placeholder="اختر كلمة مرور قوية" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="confirmPassword">تأكيد كلمة المرور</label>
                        <div class="input-container">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="confirmPassword" name="confirmPassword" placeholder="أعد إدخال كلمة المرور" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="fullName">الاسم الكامل</label>
                        <div class="input-container">
                            <i class="fas fa-id-card"></i>
                            <input type="text" id="fullName" name="fullName" placeholder="أدخل اسمك الكامل" required>
                        </div>
                    </div>
                    
                    <button type="submit" class="register-submit-btn">
                        <i class="fas fa-user-plus"></i>
                        إنشاء الحساب
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Forgot Password Modal -->
        <div id="forgotPasswordModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>استعادة كلمة المرور</h3>
                    <span class="close" onclick="closeForgotPasswordModal()">&times;</span>
                </div>
                <form id="forgotPasswordForm" class="forgot-password-form">
                    <p>أدخل اسم المستخدم أو البريد الإلكتروني لاستعادة كلمة المرور</p>
                    
                    <div class="form-group">
                        <label for="resetIdentifier">اسم المستخدم أو البريد الإلكتروني</label>
                        <div class="input-container">
                            <i class="fas fa-envelope"></i>
                            <input type="text" id="resetIdentifier" name="resetIdentifier" placeholder="أدخل اسم المستخدم أو البريد الإلكتروني" required>
                        </div>
                    </div>
                    
                    <button type="submit" class="reset-btn">
                        <i class="fas fa-paper-plane"></i>
                        إرسال رابط الاستعادة
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>جاري تسجيل الدخول...</p>
        </div>
    </div>
    
    <script src="login-script.js"></script>
</body>
</html>
