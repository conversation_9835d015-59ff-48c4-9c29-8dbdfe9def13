<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الحضور والانصراف - EG BANK</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo-section">
                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjUwIiB2aWV3Qm94PSIwIDAgMTAwIDUwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjUwIiBmaWxsPSIjMkE0MDU5Ii8+Cjx0ZXh0IHg9IjUwIiB5PSIzMCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE2IiBmb250LXdlaWdodD0iYm9sZCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkVHIEJBTks8L3RleHQ+Cjwvc3ZnPgo=" alt="EG BANK" class="logo">
                <h1>نظام الحضور والانصراف</h1>
            </div>
            <div class="current-time" id="currentTime"></div>
        </header>

        <!-- Control Panel -->
        <div class="control-panel">
            <div class="input-section">
                <div class="form-group">
                    <label for="employeeName">اسم الموظف:</label>
                    <input type="text" id="employeeName" placeholder="أدخل اسم الموظف">
                </div>
                <div class="form-group">
                    <label for="employeeId">رقم الموظف:</label>
                    <input type="text" id="employeeId" placeholder="أدخل رقم الموظف">
                </div>
                <div class="form-group">
                    <label for="employeePhoto">صورة الموظف:</label>
                    <div class="photo-upload-container">
                        <input type="file" id="employeePhoto" accept="image/*" onchange="handlePhotoUpload(event)">
                        <div class="photo-preview" id="photoPreview">
                            <i class="fas fa-camera"></i>
                            <span>اختر صورة الموظف</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="action-buttons">
                <button class="btn btn-checkin" onclick="recordAttendance('checkin')">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل حضور
                </button>
                <button class="btn btn-checkout" onclick="recordAttendance('checkout')">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل انصراف
                </button>
                <button class="btn btn-speak" onclick="speakName()">
                    <i class="fas fa-volume-up"></i>
                    نطق الاسم
                </button>
            </div>
        </div>

        <!-- Statistics -->
        <div class="stats-section">
            <div class="stat-card">
                <i class="fas fa-users"></i>
                <div class="stat-info">
                    <span class="stat-number" id="totalEmployees">0</span>
                    <span class="stat-label">إجمالي الموظفين</span>
                </div>
            </div>
            <div class="stat-card">
                <i class="fas fa-user-check"></i>
                <div class="stat-info">
                    <span class="stat-number" id="presentToday">0</span>
                    <span class="stat-label">الحاضرين اليوم</span>
                </div>
            </div>
            <div class="stat-card">
                <i class="fas fa-clock"></i>
                <div class="stat-info">
                    <span class="stat-number" id="avgWorkHours">0</span>
                    <span class="stat-label">متوسط ساعات العمل</span>
                </div>
            </div>
        </div>

        <!-- Search and Filter -->
        <div class="search-section">
            <div class="search-controls">
                <input type="text" id="searchInput" placeholder="البحث بالاسم أو الرقم..." onkeyup="filterTable()">
                <input type="date" id="dateFilter" onchange="filterTable()">
                <select id="statusFilter" onchange="filterTable()">
                    <option value="">جميع الحالات</option>
                    <option value="حضور">حضور فقط</option>
                    <option value="انصراف">انصراف فقط</option>
                </select>
            </div>
        </div>

        <!-- Attendance Table -->
        <div class="table-section">
            <div class="table-header">
                <h2>سجل الحضور والانصراف</h2>
                <button class="btn btn-export" onclick="exportToExcel()">
                    <i class="fas fa-file-excel"></i>
                    تصدير Excel
                </button>
            </div>
            
            <div class="table-container">
                <table id="attendanceTable">
                    <thead>
                        <tr>
                            <th>الصورة</th>
                            <th>رقم الموظف</th>
                            <th>اسم الموظف</th>
                            <th>التاريخ</th>
                            <th>وقت الحضور</th>
                            <th>وقت الانصراف</th>
                            <th>ساعات العمل</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="attendanceTableBody">
                        <!-- Data will be populated here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Edit Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تعديل السجل</h3>
                <span class="close" onclick="closeEditModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="editForm">
                    <input type="hidden" id="editRecordId">
                    <div class="form-group">
                        <label>اسم الموظف:</label>
                        <input type="text" id="editEmployeeName" required>
                    </div>
                    <div class="form-group">
                        <label>رقم الموظف:</label>
                        <input type="text" id="editEmployeeId" required>
                    </div>
                    <div class="form-group">
                        <label>صورة الموظف:</label>
                        <div class="photo-upload-container">
                            <input type="file" id="editEmployeePhoto" accept="image/*" onchange="handleEditPhotoUpload(event)">
                            <div class="photo-preview" id="editPhotoPreview">
                                <i class="fas fa-camera"></i>
                                <span>اختر صورة الموظف</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>التاريخ:</label>
                        <input type="date" id="editDate" required>
                    </div>
                    <div class="form-group">
                        <label>وقت الحضور:</label>
                        <input type="time" id="editCheckinTime">
                    </div>
                    <div class="form-group">
                        <label>وقت الانصراف:</label>
                        <input type="time" id="editCheckoutTime">
                    </div>
                    <div class="modal-actions">
                        <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                        <button type="button" class="btn btn-secondary" onclick="closeEditModal()">إلغاء</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Photo View Modal -->
    <div id="photoModal" class="modal">
        <div class="modal-content photo-modal-content">
            <div class="modal-header">
                <h3 id="photoModalTitle">صورة الموظف</h3>
                <span class="close" onclick="closePhotoModal()">&times;</span>
            </div>
            <div class="modal-body photo-modal-body">
                <img id="photoModalImage" src="" alt="صورة الموظف">
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
