// Global Variables
let currentFilter = 'all';
let wishlistItems = JSON.parse(localStorage.getItem('wishlistItems')) || [];
let currentProduct = null;
let currentBrand = null;

// Sample Products Data
const products = [
    {
        id: 1,
        title: "إنفوجرافيك الأعمال المتقدم",
        description: "مجموعة شاملة من قوالب الإنفوجرافيك للشركات والمؤسسات",
        price: "299 جنيه",
        category: "infographics",
        image: "https://via.placeholder.com/300x200/667eea/ffffff?text=Infographic+1"
    },
    {
        id: 2,
        title: "عرض تقديمي احترافي",
        description: "قالب PowerPoint متميز للعروض التقديمية المهنية",
        price: "199 جنيه",
        category: "presentations",
        image: "https://via.placeholder.com/300x200/764ba2/ffffff?text=Presentation+1"
    },
    {
        id: 3,
        title: "تصميم هوية الشركات",
        description: "حزمة كاملة لتصميم هوية بصرية متكاملة للشركات",
        price: "499 جنيه",
        category: "business",
        image: "https://via.placeholder.com/300x200/f093fb/ffffff?text=Business+1"
    },
    {
        id: 4,
        title: "مواد تسويقية إبداعية",
        description: "مجموعة من التصاميم التسويقية للحملات الإعلانية",
        price: "349 جنيه",
        category: "marketing",
        image: "https://via.placeholder.com/300x200/4facfe/ffffff?text=Marketing+1"
    },
    {
        id: 5,
        title: "إنفوجرافيك البيانات",
        description: "قوالب متخصصة لعرض البيانات والإحصائيات",
        price: "249 جنيه",
        category: "infographics",
        image: "https://via.placeholder.com/300x200/667eea/ffffff?text=Infographic+2"
    },
    {
        id: 6,
        title: "عرض المنتجات",
        description: "قوالب لعرض المنتجات والخدمات بطريقة جذابة",
        price: "179 جنيه",
        category: "presentations",
        image: "https://via.placeholder.com/300x200/764ba2/ffffff?text=Presentation+2"
    },
    {
        id: 7,
        title: "تقارير الأعمال",
        description: "قوالب احترافية لإعداد التقارير السنوية والشهرية",
        price: "399 جنيه",
        category: "business",
        image: "https://via.placeholder.com/300x200/f093fb/ffffff?text=Business+2"
    },
    {
        id: 8,
        title: "حملات وسائل التواصل",
        description: "تصاميم متنوعة لحملات وسائل التواصل الاجتماعي",
        price: "229 جنيه",
        category: "marketing",
        image: "https://via.placeholder.com/300x200/4facfe/ffffff?text=Marketing+2"
    },
    {
        id: 9,
        title: "رسوم بيانية تفاعلية",
        description: "مجموعة من الرسوم البيانية التفاعلية والحديثة",
        price: "319 جنيه",
        category: "infographics",
        image: "https://via.placeholder.com/300x200/667eea/ffffff?text=Infographic+3"
    },
    {
        id: 10,
        title: "عروض المبيعات",
        description: "قوالب مخصصة لعروض المبيعات والعملاء",
        price: "279 جنيه",
        category: "presentations",
        image: "https://via.placeholder.com/300x200/764ba2/ffffff?text=Presentation+3"
    },
    {
        id: 11,
        title: "استراتيجية الأعمال",
        description: "قوالب لعرض استراتيجيات الأعمال والخطط المستقبلية",
        price: "449 جنيه",
        category: "business",
        image: "https://via.placeholder.com/300x200/f093fb/ffffff?text=Business+3"
    },
    {
        id: 12,
        title: "تحليل السوق",
        description: "أدوات بصرية لتحليل السوق والمنافسين",
        price: "369 جنيه",
        category: "marketing",
        image: "https://via.placeholder.com/300x200/4facfe/ffffff?text=Marketing+3"
    }
];

// Security Brands Data
const securityBrands = {
    shahbaco: {
        name: 'شهابكو',
        tagline: 'رائدة في مجال الأنظمة الأمنية وكاميرات المراقبة',
        logo: 'fas fa-shield-alt',
        color: '#2c3e50',
        founded: '1995',
        countries: '25+',
        certifications: '15+',
        overview: `شركة شهابكو هي إحدى الشركات الرائدة في مجال الأنظمة الأمنية وكاميرات المراقبة في الشرق الأوسط. تأسست الشركة عام 1995 وتخصصت في تقديم حلول أمنية متكاملة للمؤسسات والشركات والمنازل.

تتميز شهابكو بخبرتها الواسعة في مجال التكنولوجيا الأمنية وتقديم أحدث الحلول التقنية المتطورة. الشركة حاصلة على عدة شهادات جودة عالمية وتعمل مع أفضل الموردين العالميين لضمان جودة منتجاتها.`,
        products: [
            { name: 'كاميرات IP', icon: 'fas fa-video', description: 'كاميرات شبكة عالية الدقة مع تقنيات متطورة' },
            { name: 'أنظمة الإنذار', icon: 'fas fa-bell', description: 'أنظمة إنذار ذكية للحماية الشاملة' },
            { name: 'تحكم الوصول', icon: 'fas fa-key', description: 'أنظمة تحكم في الوصول والدخول' },
            { name: 'أجهزة التسجيل', icon: 'fas fa-hdd', description: 'أجهزة تسجيل رقمية عالية الأداء' }
        ],
        features: [
            { title: 'جودة عالية', icon: 'fas fa-star', description: 'منتجات عالية الجودة مع ضمان شامل' },
            { title: 'دعم فني', icon: 'fas fa-headset', description: 'دعم فني متخصص على مدار الساعة' },
            { title: 'تركيب احترافي', icon: 'fas fa-tools', description: 'فريق تركيب وصيانة محترف' },
            { title: 'أسعار تنافسية', icon: 'fas fa-dollar-sign', description: 'أسعار مناسبة مع جودة عالية' }
        ],
        contact: {
            phone: '+20 2 1234 5678',
            email: '<EMAIL>',
            website: 'www.shahbaco.com',
            address: 'القاهرة، مصر'
        }
    },
    techno: {
        name: 'تكنو',
        tagline: 'حلول تقنية متطورة للمراقبة والأمان',
        logo: 'fas fa-video',
        color: '#e74c3c',
        founded: '2000',
        countries: '30+',
        certifications: '20+',
        overview: `شركة تكنو متخصصة في تقديم الحلول التقنية المتطورة في مجال المراقبة والأمان. تأسست عام 2000 وأصبحت من الشركات الرائدة في استخدام تقنيات الذكاء الاصطناعي وتحليل الفيديو.

تركز تكنو على الابتكار والتطوير المستمر لتقديم أحدث التقنيات في مجال الأمان. الشركة تعمل مع أكبر الشركات العالمية وتقدم حلول مخصصة لكل عميل حسب احتياجاته.`,
        products: [
            { name: 'كاميرات ذكية', icon: 'fas fa-brain', description: 'كاميرات مزودة بتقنيات الذكاء الاصطناعي' },
            { name: 'تحليل الفيديو', icon: 'fas fa-chart-line', description: 'برامج تحليل الفيديو المتقدمة' },
            { name: 'مراقبة ذكية', icon: 'fas fa-eye', description: 'أنظمة مراقبة ذكية تفاعلية' },
            { name: 'التحكم عن بعد', icon: 'fas fa-mobile-alt', description: 'تطبيقات التحكم عن بعد' }
        ],
        features: [
            { title: 'ذكاء اصطناعي', icon: 'fas fa-robot', description: 'تقنيات ذكاء اصطناعي متطورة' },
            { title: 'تحليل متقدم', icon: 'fas fa-analytics', description: 'تحليل البيانات والسلوكيات' },
            { title: 'سحابة آمنة', icon: 'fas fa-cloud', description: 'تخزين سحابي آمن ومشفر' },
            { title: 'تحديثات مستمرة', icon: 'fas fa-sync', description: 'تحديثات برمجية مستمرة' }
        ],
        contact: {
            phone: '+20 2 2345 6789',
            email: '<EMAIL>',
            website: 'www.techno-security.com',
            address: 'الجيزة، مصر'
        }
    },
    hikvision: {
        name: 'هيك فيجن',
        tagline: 'العلامة التجارية الرائدة عالمياً في كاميرات المراقبة',
        logo: 'fas fa-eye',
        color: '#3498db',
        founded: '2001',
        countries: '150+',
        certifications: '50+',
        overview: `هيك فيجن هي العلامة التجارية الرائدة عالمياً في مجال كاميرات المراقبة والأنظمة الأمنية. تأسست عام 2001 في الصين وأصبحت أكبر مصنع لكاميرات المراقبة في العالم.

تتميز هيك فيجن بالابتكار المستمر والاستثمار الكبير في البحث والتطوير. الشركة تقدم مجموعة واسعة من المنتجات من الكاميرات الأساسية إلى الحلول المتطورة للذكاء الاصطناعي.`,
        products: [
            { name: 'كاميرات 4K', icon: 'fas fa-video', description: 'كاميرات عالية الدقة 4K Ultra HD' },
            { name: 'رؤية ليلية', icon: 'fas fa-moon', description: 'تقنيات رؤية ليلية متطورة' },
            { name: 'مقاومة الطقس', icon: 'fas fa-cloud-rain', description: 'كاميرات مقاومة للعوامل الجوية' },
            { name: 'تسجيل سحابي', icon: 'fas fa-cloud-upload-alt', description: 'خدمات التسجيل السحابي' }
        ],
        features: [
            { title: 'جودة فائقة', icon: 'fas fa-gem', description: 'جودة صورة فائقة الوضوح' },
            { title: 'تقنيات متطورة', icon: 'fas fa-microchip', description: 'أحدث التقنيات في المراقبة' },
            { title: 'شبكة عالمية', icon: 'fas fa-globe', description: 'شبكة دعم عالمية' },
            { title: 'ضمان شامل', icon: 'fas fa-shield-check', description: 'ضمان شامل على جميع المنتجات' }
        ],
        contact: {
            phone: '+20 2 3456 7890',
            email: '<EMAIL>',
            website: 'www.hikvision.com',
            address: 'القاهرة الجديدة، مصر'
        }
    },
    dahua: {
        name: 'داهوا',
        tagline: 'حلول أمنية شاملة مع تقنيات متقدمة',
        logo: 'fas fa-camera',
        color: '#9b59b6',
        founded: '2001',
        countries: '180+',
        certifications: '40+',
        overview: `داهوا هي شركة رائدة عالمياً في مجال حلول الأمان والمراقبة الذكية. تأسست عام 2001 وتركز على تطوير تقنيات الذكاء الاصطناعي وإنترنت الأشياء في مجال الأمان.

تقدم داهوا حلول شاملة تشمل الكاميرات الذكية، أنظمة التحكم في الوصول، وحلول المدن الذكية. الشركة معروفة بتقنية WizSense المتطورة للذكاء الاصطناعي.`,
        products: [
            { name: 'تقنية WizSense', icon: 'fas fa-magic', description: 'تقنية ذكاء اصطناعي متطورة' },
            { name: 'كشف الحركة', icon: 'fas fa-running', description: 'أنظمة كشف الحركة الذكية' },
            { name: 'تسجيل سحابي', icon: 'fas fa-cloud', description: 'حلول التسجيل السحابي' },
            { name: 'تحليل السلوك', icon: 'fas fa-user-check', description: 'تحليل السلوك والأنماط' }
        ],
        features: [
            { title: 'ذكاء اصطناعي', icon: 'fas fa-brain', description: 'تقنيات ذكاء اصطناعي متقدمة' },
            { title: 'دقة عالية', icon: 'fas fa-crosshairs', description: 'دقة عالية في التعرف والتحليل' },
            { title: 'سهولة الاستخدام', icon: 'fas fa-user-friendly', description: 'واجهات سهلة الاستخدام' },
            { title: 'تكامل شامل', icon: 'fas fa-puzzle-piece', description: 'تكامل مع الأنظمة الأخرى' }
        ],
        contact: {
            phone: '+20 2 4567 8901',
            email: '<EMAIL>',
            website: 'www.dahuatech.com',
            address: 'الإسكندرية، مصر'
        }
    },
    axis: {
        name: 'أكسيس',
        tagline: 'رائدة في كاميرات الشبكة والحلول الأمنية',
        logo: 'fas fa-cog',
        color: '#27ae60',
        founded: '1984',
        countries: '50+',
        certifications: '30+',
        overview: `أكسيس هي الشركة الرائدة عالمياً في مجال كاميرات الشبكة والحلول الأمنية المتطورة. تأسست عام 1984 في السويد وهي رائدة في تطوير تقنيات كاميرات IP.

تركز أكسيس على الابتكار والجودة العالية، وتقدم حلول متكاملة للمراقبة والأمان. الشركة معروفة بموثوقيتها العالية وتقنياتها المتطورة في مجال التحليلات والذكاء الاصطناعي.`,
        products: [
            { name: 'كاميرات شبكة', icon: 'fas fa-network-wired', description: 'كاميرات IP متطورة' },
            { name: 'تحليلات متقدمة', icon: 'fas fa-chart-bar', description: 'برامج تحليل متقدمة' },
            { name: 'جودة عالية', icon: 'fas fa-hd-video', description: 'جودة صورة فائقة' },
            { name: 'حلول متكاملة', icon: 'fas fa-layer-group', description: 'حلول أمنية متكاملة' }
        ],
        features: [
            { title: 'موثوقية عالية', icon: 'fas fa-check-circle', description: 'موثوقية وأداء عالي' },
            { title: 'تقنيات متطورة', icon: 'fas fa-rocket', description: 'أحدث التقنيات السويدية' },
            { title: 'سهولة التركيب', icon: 'fas fa-wrench', description: 'تركيب وصيانة سهلة' },
            { title: 'دعم ممتاز', icon: 'fas fa-life-ring', description: 'دعم فني ممتاز' }
        ],
        contact: {
            phone: '+20 2 5678 9012',
            email: '<EMAIL>',
            website: 'www.axis.com',
            address: 'القاهرة، مصر'
        }
    },
    bosch: {
        name: 'بوش',
        tagline: 'حلول أمنية موثوقة مع تقنيات ألمانية',
        logo: 'fas fa-tools',
        color: '#f39c12',
        founded: '1886',
        countries: '60+',
        certifications: '100+',
        overview: `بوش هي شركة ألمانية عريقة تأسست عام 1886 وهي من الرواد العالميين في مجال التكنولوجيا والخدمات. قسم الأمان في بوش يقدم حلول أمنية متطورة وموثوقة.

تتميز بوش بالجودة الألمانية العالية والابتكار المستمر. الشركة تقدم مجموعة واسعة من المنتجات الأمنية من كاميرات المراقبة إلى أنظمة الإنذار المتطورة.`,
        products: [
            { name: 'جودة ألمانية', icon: 'fas fa-flag', description: 'منتجات بجودة ألمانية عالية' },
            { name: 'موثوقية عالية', icon: 'fas fa-award', description: 'موثوقية وأداء استثنائي' },
            { name: 'تقنيات متطورة', icon: 'fas fa-cogs', description: 'أحدث التقنيات الألمانية' },
            { name: 'حلول شاملة', icon: 'fas fa-briefcase', description: 'حلول أمنية شاملة' }
        ],
        features: [
            { title: 'خبرة عريقة', icon: 'fas fa-history', description: 'خبرة أكثر من 130 عام' },
            { title: 'جودة فائقة', icon: 'fas fa-star', description: 'جودة ألمانية معتمدة' },
            { title: 'ابتكار مستمر', icon: 'fas fa-lightbulb', description: 'استثمار كبير في البحث والتطوير' },
            { title: 'شبكة عالمية', icon: 'fas fa-globe-europe', description: 'شبكة خدمة عالمية' }
        ],
        contact: {
            phone: '+20 2 6789 0123',
            email: '<EMAIL>',
            website: 'www.boschsecurity.com',
            address: 'القاهرة، مصر'
        }
    }
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    // Check authentication first
    if (!checkAuthentication()) {
        return; // Will redirect to login
    }

    // Initialize store if authenticated
    initializeStore();
});

// Check if user is authenticated
function checkAuthentication() {
    const currentUser = localStorage.getItem('egbank_current_user');
    const loginTime = localStorage.getItem('egbank_login_time');

    if (!currentUser || !loginTime) {
        redirectToLogin();
        return false;
    }

    // Check if login is still valid (24 hours)
    const loginDate = new Date(loginTime);
    const now = new Date();
    const hoursDiff = (now - loginDate) / (1000 * 60 * 60);

    if (hoursDiff >= 24) {
        // Session expired
        localStorage.removeItem('egbank_current_user');
        localStorage.removeItem('egbank_login_time');
        showNotification('انتهت صلاحية جلسة العمل. يرجى تسجيل الدخول مرة أخرى', 'warning');
        setTimeout(() => {
            redirectToLogin();
        }, 2000);
        return false;
    }

    // Update user info in header
    updateUserInfo(currentUser);
    return true;
}

// Initialize store functionality
function initializeStore() {
    loadProducts();
    updateWishlistCount();

    // Add smooth scrolling
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Update user info in header
function updateUserInfo(username) {
    const users = JSON.parse(localStorage.getItem('egbank_users')) || {};
    const user = users[username];

    if (user) {
        // Add user info to header
        const headerActions = document.querySelector('.header-actions');
        const userInfo = document.createElement('div');
        userInfo.className = 'user-info';
        userInfo.innerHTML = `
            <div class="user-dropdown">
                <div class="user-avatar" onclick="toggleUserMenu()">
                    <i class="fas fa-user"></i>
                    <span>${user.fullName || username}</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="user-menu" id="userMenu">
                    <div class="user-menu-header">
                        <strong>${user.fullName || username}</strong>
                        <small>${user.email}</small>
                    </div>
                    <div class="user-menu-divider"></div>
                    <a href="#" onclick="showProfile()">
                        <i class="fas fa-user-cog"></i>
                        الملف الشخصي
                    </a>
                    <a href="#" onclick="showOrders()">
                        <i class="fas fa-shopping-bag"></i>
                        طلباتي
                    </a>
                    <a href="#" onclick="showSettings()">
                        <i class="fas fa-cog"></i>
                        الإعدادات
                    </a>
                    <div class="user-menu-divider"></div>
                    <a href="#" onclick="logout()" class="logout-link">
                        <i class="fas fa-sign-out-alt"></i>
                        تسجيل الخروج
                    </a>
                </div>
            </div>
        `;

        // Insert before wishlist
        headerActions.insertBefore(userInfo, headerActions.firstChild);
    }
}

// Redirect to login page
function redirectToLogin() {
    window.location.href = 'login.html';
}

// Logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('egbank_current_user');
        localStorage.removeItem('egbank_login_time');
        showNotification('تم تسجيل الخروج بنجاح', 'success');
        setTimeout(() => {
            redirectToLogin();
        }, 1500);
    }
}

// Toggle user menu
function toggleUserMenu() {
    const userMenu = document.getElementById('userMenu');
    userMenu.style.display = userMenu.style.display === 'block' ? 'none' : 'block';
}

// Close user menu when clicking outside
document.addEventListener('click', function(event) {
    const userDropdown = document.querySelector('.user-dropdown');
    const userMenu = document.getElementById('userMenu');

    if (userMenu && userDropdown && !userDropdown.contains(event.target)) {
        userMenu.style.display = 'none';
    }

    // Close brand modal when clicking outside
    const brandModal = document.getElementById('brandModal');
    if (brandModal && event.target === brandModal) {
        closeBrandModal();
    }
});

// Placeholder functions for user menu items
function showProfile() {
    showNotification('صفحة الملف الشخصي قيد التطوير', 'info');
    toggleUserMenu();
}

function showOrders() {
    showNotification('صفحة الطلبات قيد التطوير', 'info');
    toggleUserMenu();
}

function showSettings() {
    showNotification('صفحة الإعدادات قيد التطوير', 'info');
    toggleUserMenu();
}

// Brand Functions
function openBrandDetails(brandId) {
    currentBrand = securityBrands[brandId];
    if (!currentBrand) return;

    // Update modal content
    updateBrandModal();

    // Show modal
    document.getElementById('brandModal').style.display = 'block';
    document.body.style.overflow = 'hidden';
}

function updateBrandModal() {
    if (!currentBrand) return;

    // Update header
    document.getElementById('brandModalLogo').innerHTML = `<i class="${currentBrand.logo}" style="color: ${currentBrand.color}"></i>`;
    document.getElementById('brandModalName').textContent = currentBrand.name;
    document.getElementById('brandModalTagline').textContent = currentBrand.tagline;

    // Update header background
    const header = document.querySelector('.brand-modal-header');
    header.style.background = `linear-gradient(135deg, ${currentBrand.color}, ${adjustColor(currentBrand.color, -20)})`;

    // Update overview tab
    document.getElementById('brandOverviewText').innerHTML = currentBrand.overview.replace(/\n/g, '<br><br>');
    document.getElementById('brandFoundedYear').textContent = currentBrand.founded;
    document.getElementById('brandCountries').textContent = currentBrand.countries;
    document.getElementById('brandCertifications').textContent = currentBrand.certifications;

    // Update products tab
    updateBrandProducts();

    // Update features tab
    updateBrandFeatures();

    // Update contact tab
    updateBrandContact();
}

function updateBrandProducts() {
    const productsList = document.getElementById('brandProductsList');
    productsList.innerHTML = '';

    currentBrand.products.forEach(product => {
        const productItem = document.createElement('div');
        productItem.className = 'product-item';
        productItem.innerHTML = `
            <i class="${product.icon}"></i>
            <h4>${product.name}</h4>
            <p>${product.description}</p>
        `;
        productsList.appendChild(productItem);
    });
}

function updateBrandFeatures() {
    const featuresList = document.getElementById('brandFeaturesList');
    featuresList.innerHTML = '';

    currentBrand.features.forEach(feature => {
        const featureItem = document.createElement('div');
        featureItem.className = 'feature-item';
        featureItem.innerHTML = `
            <i class="${feature.icon}"></i>
            <div>
                <h4>${feature.title}</h4>
                <p>${feature.description}</p>
            </div>
        `;
        featuresList.appendChild(featureItem);
    });
}

function updateBrandContact() {
    const contactInfo = document.getElementById('brandContactInfo');
    contactInfo.innerHTML = `
        <div class="contact-item">
            <i class="fas fa-phone"></i>
            <h4>الهاتف</h4>
            <p>${currentBrand.contact.phone}</p>
        </div>
        <div class="contact-item">
            <i class="fas fa-envelope"></i>
            <h4>البريد الإلكتروني</h4>
            <p>${currentBrand.contact.email}</p>
        </div>
        <div class="contact-item">
            <i class="fas fa-globe"></i>
            <h4>الموقع الإلكتروني</h4>
            <p>${currentBrand.contact.website}</p>
        </div>
        <div class="contact-item">
            <i class="fas fa-map-marker-alt"></i>
            <h4>العنوان</h4>
            <p>${currentBrand.contact.address}</p>
        </div>
    `;
}

function showBrandTab(tabName) {
    // Hide all tabs
    document.querySelectorAll('.tab-panel').forEach(panel => {
        panel.classList.remove('active');
    });

    // Remove active class from all buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // Show selected tab
    document.getElementById(tabName).classList.add('active');

    // Add active class to clicked button
    event.target.classList.add('active');
}

function closeBrandModal() {
    document.getElementById('brandModal').style.display = 'none';
    document.body.style.overflow = 'auto';
    currentBrand = null;
}

function requestQuote() {
    if (!currentBrand) return;

    showNotification(`تم إرسال طلب عرض سعر لمنتجات ${currentBrand.name}`, 'success');

    // Here you would typically send the request to your backend
    console.log(`Quote requested for ${currentBrand.name}`);
}

function downloadCatalog() {
    if (!currentBrand) return;

    showNotification(`جاري تحميل كتالوج ${currentBrand.name}...`, 'info');

    // Here you would typically trigger a download
    console.log(`Catalog download for ${currentBrand.name}`);
}

// Helper function to adjust color brightness
function adjustColor(color, amount) {
    const usePound = color[0] === '#';
    const col = usePound ? color.slice(1) : color;
    const num = parseInt(col, 16);
    let r = (num >> 16) + amount;
    let g = (num >> 8 & 0x00FF) + amount;
    let b = (num & 0x0000FF) + amount;
    r = r > 255 ? 255 : r < 0 ? 0 : r;
    g = g > 255 ? 255 : g < 0 ? 0 : g;
    b = b > 255 ? 255 : b < 0 ? 0 : b;
    return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0');
}

// Show specific section
function showSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.content-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // Show selected section
    document.getElementById(sectionId).classList.add('active');
    
    // Update navigation
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    event.target.classList.add('active');
}

// Load and display products
function loadProducts(filter = 'all') {
    const productsGrid = document.getElementById('productsGrid');
    productsGrid.innerHTML = '';
    
    const filteredProducts = filter === 'all' ? products : products.filter(product => product.category === filter);
    
    filteredProducts.forEach(product => {
        const productCard = createProductCard(product);
        productsGrid.appendChild(productCard);
    });
    
    // Add animation
    setTimeout(() => {
        document.querySelectorAll('.product-card').forEach((card, index) => {
            card.style.animation = `fadeInUp 0.6s ease ${index * 0.1}s both`;
        });
    }, 100);
}

// Create product card element
function createProductCard(product) {
    const card = document.createElement('div');
    card.className = 'product-card';
    card.onclick = () => openProductModal(product);
    
    card.innerHTML = `
        <div class="product-image">
            <img src="${product.image}" alt="${product.title}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
            <div style="display:none; width:100%; height:100%; background:linear-gradient(45deg, #667eea, #764ba2); align-items:center; justify-content:center; color:white; font-weight:600;">
                ${product.title}
            </div>
        </div>
        <div class="product-info">
            <div class="product-title">${product.title}</div>
            <div class="product-description">${product.description}</div>
            <div class="product-price">${product.price}</div>
            <div class="product-actions">
                <button class="btn-primary" onclick="event.stopPropagation(); addToCart(${product.id})">
                    <i class="fas fa-shopping-cart"></i> إضافة للسلة
                </button>
                <button class="btn-secondary" onclick="event.stopPropagation(); toggleWishlistItem(${product.id})">
                    <i class="fas fa-heart ${wishlistItems.includes(product.id) ? 'fas' : 'far'}"></i>
                </button>
            </div>
        </div>
    `;
    
    return card;
}

// Filter products
function filterProducts(category) {
    currentFilter = category;
    loadProducts(category);
    
    // Update filter buttons
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    event.target.classList.add('active');
    
    // Show notification
    const categoryNames = {
        'all': 'جميع المنتجات',
        'infographics': 'الإنفوجرافيك',
        'presentations': 'العروض التقديمية',
        'business': 'الأعمال',
        'marketing': 'التسويق'
    };
    
    showNotification(`تم تصفية المنتجات: ${categoryNames[category]}`, 'info');
}

// Search functionality
function performSearch() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
    
    if (!searchTerm) {
        loadProducts(currentFilter);
        return;
    }
    
    const filteredProducts = products.filter(product => 
        product.title.toLowerCase().includes(searchTerm) ||
        product.description.toLowerCase().includes(searchTerm)
    );
    
    const productsGrid = document.getElementById('productsGrid');
    productsGrid.innerHTML = '';
    
    if (filteredProducts.length === 0) {
        productsGrid.innerHTML = `
            <div style="grid-column: 1/-1; text-align: center; padding: 40px; color: #666;">
                <i class="fas fa-search" style="font-size: 48px; margin-bottom: 20px; opacity: 0.5;"></i>
                <h3>لم يتم العثور على منتجات</h3>
                <p>جرب البحث بكلمات مختلفة</p>
            </div>
        `;
    } else {
        filteredProducts.forEach(product => {
            const productCard = createProductCard(product);
            productsGrid.appendChild(productCard);
        });
        
        showNotification(`تم العثور على ${filteredProducts.length} منتج`, 'success');
    }
}

// Add Enter key support for search
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
});

// Scroll to products section
function scrollToProducts() {
    document.getElementById('products-section').scrollIntoView({
        behavior: 'smooth'
    });
}

// Open product modal
function openProductModal(product) {
    currentProduct = product;
    
    document.getElementById('modalProductImage').src = product.image;
    document.getElementById('modalProductTitle').textContent = product.title;
    document.getElementById('modalProductDescription').textContent = product.description;
    document.getElementById('modalProductPrice').textContent = product.price;
    
    document.getElementById('productModal').style.display = 'block';
    document.body.style.overflow = 'hidden';
}

// Close product modal
function closeProductModal() {
    document.getElementById('productModal').style.display = 'none';
    document.body.style.overflow = 'auto';
    currentProduct = null;
}

// Add to cart
function addToCart(productId = null) {
    const id = productId || (currentProduct ? currentProduct.id : null);
    if (!id) return;
    
    const product = products.find(p => p.id === id);
    if (product) {
        showNotification(`تم إضافة "${product.title}" إلى السلة`, 'success');
        
        // Close modal if open
        if (document.getElementById('productModal').style.display === 'block') {
            closeProductModal();
        }
    }
}

// Toggle wishlist item
function toggleWishlistItem(productId) {
    const index = wishlistItems.indexOf(productId);
    const product = products.find(p => p.id === productId);

    if (index > -1) {
        wishlistItems.splice(index, 1);
        showNotification(`تم إزالة "${product.title}" من المفضلة`, 'info');
    } else {
        wishlistItems.push(productId);
        showNotification(`تم إضافة "${product.title}" إلى المفضلة`, 'success');
    }

    localStorage.setItem('wishlistItems', JSON.stringify(wishlistItems));
    updateWishlistCount();

    // Update heart icons
    updateWishlistIcons();

    // If wishlist modal is open, refresh it
    const wishlistModal = document.getElementById('wishlistModal');
    if (wishlistModal.style.display === 'block') {
        // Refresh wishlist modal content
        setTimeout(() => {
            const wishlistItemsContainer = document.getElementById('wishlistItems');
            wishlistItemsContainer.innerHTML = '';

            if (wishlistItems.length === 0) {
                wishlistItemsContainer.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <i class="fas fa-heart" style="font-size: 48px; margin-bottom: 20px; opacity: 0.5;"></i>
                        <h3>قائمة المفضلة فارغة</h3>
                        <p>أضف بعض المنتجات إلى قائمة المفضلة</p>
                    </div>
                `;
            } else {
                wishlistItems.forEach(id => {
                    const prod = products.find(p => p.id === id);
                    if (prod) {
                        const wishlistItem = document.createElement('div');
                        wishlistItem.className = 'wishlist-item';
                        wishlistItem.id = `wishlist-item-${prod.id}`;
                        wishlistItem.style.cssText = `
                            display: flex;
                            align-items: center;
                            gap: 15px;
                            padding: 15px;
                            border: 1px solid #e9ecef;
                            border-radius: 10px;
                            margin-bottom: 15px;
                            transition: all 0.3s ease;
                        `;

                        wishlistItem.innerHTML = `
                            <img src="${prod.image}" alt="${prod.title}" style="width: 80px; height: 60px; object-fit: cover; border-radius: 5px;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div style="display:none; width:80px; height:60px; background:linear-gradient(45deg, #667eea, #764ba2); align-items:center; justify-content:center; color:white; font-size:12px; border-radius:5px; text-align:center;">
                                ${prod.title.substring(0, 10)}...
                            </div>
                            <div style="flex: 1;">
                                <h4 style="margin-bottom: 5px; color: #333;">${prod.title}</h4>
                                <p style="color: #666; font-size: 14px;">${prod.price}</p>
                            </div>
                            <div style="display: flex; gap: 10px;">
                                <button onclick="addToCartFromWishlist(${prod.id})" style="background: #7c4dff; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer; transition: background 0.3s ease;" onmouseover="this.style.background='#6a3de8'" onmouseout="this.style.background='#7c4dff'">
                                    <i class="fas fa-shopping-cart"></i>
                                </button>
                                <button onclick="removeFromWishlist(${prod.id})" style="background: #ff4757; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer; transition: background 0.3s ease;" onmouseover="this.style.background='#ff3742'" onmouseout="this.style.background='#ff4757'">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        `;

                        wishlistItemsContainer.appendChild(wishlistItem);
                    }
                });
            }
        }, 100);
    }
}

// Update wishlist count
function updateWishlistCount() {
    document.getElementById('wishlistCount').textContent = wishlistItems.length;
}

// Update wishlist icons
function updateWishlistIcons() {
    document.querySelectorAll('.product-card').forEach(card => {
        const heartIcon = card.querySelector('.btn-secondary i');
        const productId = parseInt(card.querySelector('.btn-secondary').onclick.toString().match(/\d+/)[0]);
        
        if (wishlistItems.includes(productId)) {
            heartIcon.className = 'fas fa-heart';
        } else {
            heartIcon.className = 'far fa-heart';
        }
    });
}

// Toggle wishlist modal
function toggleWishlist() {
    const modal = document.getElementById('wishlistModal');
    const wishlistItemsContainer = document.getElementById('wishlistItems');

    // Clear previous content
    wishlistItemsContainer.innerHTML = '';

    if (wishlistItems.length === 0) {
        wishlistItemsContainer.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #666;">
                <i class="fas fa-heart" style="font-size: 48px; margin-bottom: 20px; opacity: 0.5;"></i>
                <h3>قائمة المفضلة فارغة</h3>
                <p>أضف بعض المنتجات إلى قائمة المفضلة</p>
            </div>
        `;
    } else {
        wishlistItems.forEach(productId => {
            const product = products.find(p => p.id === productId);
            if (product) {
                const wishlistItem = document.createElement('div');
                wishlistItem.className = 'wishlist-item';
                wishlistItem.id = `wishlist-item-${product.id}`;
                wishlistItem.style.cssText = `
                    display: flex;
                    align-items: center;
                    gap: 15px;
                    padding: 15px;
                    border: 1px solid #e9ecef;
                    border-radius: 10px;
                    margin-bottom: 15px;
                    transition: all 0.3s ease;
                `;

                wishlistItem.innerHTML = `
                    <img src="${product.image}" alt="${product.title}" style="width: 80px; height: 60px; object-fit: cover; border-radius: 5px;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div style="display:none; width:80px; height:60px; background:linear-gradient(45deg, #667eea, #764ba2); align-items:center; justify-content:center; color:white; font-size:12px; border-radius:5px; text-align:center;">
                        ${product.title.substring(0, 10)}...
                    </div>
                    <div style="flex: 1;">
                        <h4 style="margin-bottom: 5px; color: #333;">${product.title}</h4>
                        <p style="color: #666; font-size: 14px;">${product.price}</p>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <button onclick="addToCartFromWishlist(${product.id})" style="background: #7c4dff; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer; transition: background 0.3s ease;" onmouseover="this.style.background='#6a3de8'" onmouseout="this.style.background='#7c4dff'">
                            <i class="fas fa-shopping-cart"></i>
                        </button>
                        <button onclick="removeFromWishlist(${product.id})" style="background: #ff4757; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer; transition: background 0.3s ease;" onmouseover="this.style.background='#ff3742'" onmouseout="this.style.background='#ff4757'">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                `;

                wishlistItemsContainer.appendChild(wishlistItem);
            }
        });
    }

    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';
}

// Close wishlist modal
function closeWishlistModal() {
    document.getElementById('wishlistModal').style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Submit contact form
function submitContactForm(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const name = event.target.querySelector('input[type="text"]').value;
    const email = event.target.querySelector('input[type="email"]').value;
    const message = event.target.querySelector('textarea').value;
    
    // Simulate form submission
    showNotification('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.', 'success');
    
    // Reset form
    event.target.reset();
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    
    let backgroundColor;
    switch(type) {
        case 'success':
            backgroundColor = '#4CAF50';
            break;
        case 'error':
            backgroundColor = '#f44336';
            break;
        case 'warning':
            backgroundColor = '#ff9800';
            break;
        case 'info':
        default:
            backgroundColor = '#2196F3';
            break;
    }
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${backgroundColor};
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        z-index: 1001;
        font-weight: 600;
        animation: slideInRight 0.3s ease;
        max-width: 300px;
        word-wrap: break-word;
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(100px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    @keyframes slideOutRight {
        from {
            opacity: 1;
            transform: translateX(0);
        }
        to {
            opacity: 0;
            transform: translateX(100px);
        }
    }
    
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
`;
document.head.appendChild(style);

// Close modals when clicking outside
window.onclick = function(event) {
    const productModal = document.getElementById('productModal');
    const wishlistModal = document.getElementById('wishlistModal');
    
    if (event.target === productModal) {
        closeProductModal();
    }
    
    if (event.target === wishlistModal) {
        closeWishlistModal();
    }
}

// Add to wishlist from modal
function addToWishlist() {
    if (currentProduct) {
        toggleWishlistItem(currentProduct.id);
    }
}

// Remove from wishlist with animation
function removeFromWishlist(productId) {
    const product = products.find(p => p.id === productId);
    const wishlistItem = document.getElementById(`wishlist-item-${productId}`);

    if (wishlistItem && product) {
        // Add removal animation
        wishlistItem.style.transform = 'translateX(-100%)';
        wishlistItem.style.opacity = '0';

        setTimeout(() => {
            // Remove from array
            const index = wishlistItems.indexOf(productId);
            if (index > -1) {
                wishlistItems.splice(index, 1);
                localStorage.setItem('wishlistItems', JSON.stringify(wishlistItems));
                updateWishlistCount();
                updateWishlistIcons();
            }

            // Remove from DOM
            if (wishlistItem.parentNode) {
                wishlistItem.parentNode.removeChild(wishlistItem);
            }

            // Show notification
            showNotification(`تم حذف "${product.title}" من المفضلة`, 'success');

            // Check if wishlist is empty now
            if (wishlistItems.length === 0) {
                const wishlistItemsContainer = document.getElementById('wishlistItems');
                wishlistItemsContainer.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <i class="fas fa-heart" style="font-size: 48px; margin-bottom: 20px; opacity: 0.5;"></i>
                        <h3>قائمة المفضلة فارغة</h3>
                        <p>أضف بعض المنتجات إلى قائمة المفضلة</p>
                    </div>
                `;
            }
        }, 300);
    }
}

// Add to cart from wishlist
function addToCartFromWishlist(productId) {
    const product = products.find(p => p.id === productId);
    if (product) {
        showNotification(`تم إضافة "${product.title}" إلى السلة`, 'success');

        // Optional: Remove from wishlist after adding to cart
        // removeFromWishlist(productId);
    }
}
