// Global Variables
let currentFilter = 'all';
let wishlistItems = JSON.parse(localStorage.getItem('wishlistItems')) || [];
let currentProduct = null;
let currentBrand = null;

// Sample Products Data
const products = [
    {
        id: 1,
        title: "إنفوجرافيك الأعمال المتقدم",
        description: "مجموعة شاملة من قوالب الإنفوجرافيك للشركات والمؤسسات",
        price: "299 جنيه",
        category: "infographics",
        image: "https://via.placeholder.com/300x200/667eea/ffffff?text=Infographic+1"
    },
    {
        id: 2,
        title: "عرض تقديمي احترافي",
        description: "قالب PowerPoint متميز للعروض التقديمية المهنية",
        price: "199 جنيه",
        category: "presentations",
        image: "https://via.placeholder.com/300x200/764ba2/ffffff?text=Presentation+1"
    },
    {
        id: 3,
        title: "تصميم هوية الشركات",
        description: "حزمة كاملة لتصميم هوية بصرية متكاملة للشركات",
        price: "499 جنيه",
        category: "business",
        image: "https://via.placeholder.com/300x200/f093fb/ffffff?text=Business+1"
    },
    {
        id: 4,
        title: "مواد تسويقية إبداعية",
        description: "مجموعة من التصاميم التسويقية للحملات الإعلانية",
        price: "349 جنيه",
        category: "marketing",
        image: "https://via.placeholder.com/300x200/4facfe/ffffff?text=Marketing+1"
    },
    {
        id: 5,
        title: "إنفوجرافيك البيانات",
        description: "قوالب متخصصة لعرض البيانات والإحصائيات",
        price: "249 جنيه",
        category: "infographics",
        image: "https://via.placeholder.com/300x200/667eea/ffffff?text=Infographic+2"
    },
    {
        id: 6,
        title: "عرض المنتجات",
        description: "قوالب لعرض المنتجات والخدمات بطريقة جذابة",
        price: "179 جنيه",
        category: "presentations",
        image: "https://via.placeholder.com/300x200/764ba2/ffffff?text=Presentation+2"
    },
    {
        id: 7,
        title: "تقارير الأعمال",
        description: "قوالب احترافية لإعداد التقارير السنوية والشهرية",
        price: "399 جنيه",
        category: "business",
        image: "https://via.placeholder.com/300x200/f093fb/ffffff?text=Business+2"
    },
    {
        id: 8,
        title: "حملات وسائل التواصل",
        description: "تصاميم متنوعة لحملات وسائل التواصل الاجتماعي",
        price: "229 جنيه",
        category: "marketing",
        image: "https://via.placeholder.com/300x200/4facfe/ffffff?text=Marketing+2"
    },
    {
        id: 9,
        title: "رسوم بيانية تفاعلية",
        description: "مجموعة من الرسوم البيانية التفاعلية والحديثة",
        price: "319 جنيه",
        category: "infographics",
        image: "https://via.placeholder.com/300x200/667eea/ffffff?text=Infographic+3"
    },
    {
        id: 10,
        title: "عروض المبيعات",
        description: "قوالب مخصصة لعروض المبيعات والعملاء",
        price: "279 جنيه",
        category: "presentations",
        image: "https://via.placeholder.com/300x200/764ba2/ffffff?text=Presentation+3"
    },
    {
        id: 11,
        title: "استراتيجية الأعمال",
        description: "قوالب لعرض استراتيجيات الأعمال والخطط المستقبلية",
        price: "449 جنيه",
        category: "business",
        image: "https://via.placeholder.com/300x200/f093fb/ffffff?text=Business+3"
    },
    {
        id: 12,
        title: "تحليل السوق",
        description: "أدوات بصرية لتحليل السوق والمنافسين",
        price: "369 جنيه",
        category: "marketing",
        image: "https://via.placeholder.com/300x200/4facfe/ffffff?text=Marketing+3"
    }
];

// Security Brands Data
const securityBrands = {
    hikvision: {
        name: 'Hikvision',
        tagline: 'العلامة التجارية الرائدة عالمياً في كاميرات المراقبة والأمان الذكي',
        logo: 'hikvision-logo',
        color: '#0066cc',
        founded: '2001',
        countries: '150+',
        certifications: '50+',
        overview: `هيك فيجن (Hikvision) هي الشركة الرائدة عالمياً في مجال كاميرات المراقبة والأنظمة الأمنية الذكية. تأسست عام 2001 في هانغتشو، الصين، وأصبحت أكبر مصنع لكاميرات المراقبة في العالم.

تتميز هيك فيجن بالاستثمار الكبير في البحث والتطوير، حيث تخصص أكثر من 10% من إيراداتها السنوية للابتكار. الشركة تقدم مجموعة شاملة من المنتجات تشمل كاميرات IP، أنظمة التسجيل الرقمية، وحلول الذكاء الاصطناعي المتطورة.

تخدم هيك فيجن أكثر من 150 دولة حول العالم وتوظف أكثر من 42,000 موظف. الشركة حاصلة على أكثر من 1,800 براءة اختراع وتعتبر من أكثر الشركات ابتكاراً في مجال التكنولوجيا الأمنية.`,
        products: [
            { name: 'كاميرات IP 4K/8K', icon: 'fas fa-video', description: 'كاميرات شبكة عالية الدقة تصل إلى 8K مع تقنيات متطورة' },
            { name: 'تقنية ColorVu', icon: 'fas fa-palette', description: 'رؤية ملونة على مدار 24 ساعة حتى في الظلام التام' },
            { name: 'AcuSense AI', icon: 'fas fa-brain', description: 'ذكاء اصطناعي لتمييز البشر والمركبات بدقة عالية' },
            { name: 'أنظمة التسجيل NVR', icon: 'fas fa-hdd', description: 'أجهزة تسجيل شبكة متطورة مع إدارة ذكية للبيانات' },
            { name: 'كاميرات PTZ', icon: 'fas fa-arrows-alt', description: 'كاميرات متحركة بتحكم كامل وزووم بصري عالي' },
            { name: 'حلول التحكم بالوصول', icon: 'fas fa-key', description: 'أنظمة تحكم متطورة بالوصول والحضور والانصراف' }
        ],
        features: [
            { title: 'جودة صورة فائقة', icon: 'fas fa-gem', description: 'دقة تصل إلى 8K مع تقنيات تحسين الصورة المتطورة' },
            { title: 'ذكاء اصطناعي متقدم', icon: 'fas fa-robot', description: 'تقنيات AI لتحليل السلوك وكشف الأحداث تلقائياً' },
            { title: 'شبكة دعم عالمية', icon: 'fas fa-globe', description: 'دعم فني في أكثر من 150 دولة حول العالم' },
            { title: 'أمان سيبراني', icon: 'fas fa-shield-alt', description: 'حماية متقدمة ضد التهديدات السيبرانية' },
            { title: 'سهولة التركيب', icon: 'fas fa-tools', description: 'تصميم سهل التركيب مع أدوات إعداد ذكية' },
            { title: 'ضمان شامل', icon: 'fas fa-award', description: 'ضمان عالمي شامل مع خدمة ما بعد البيع' }
        ],
        contact: {
            phone: '01002289401',
            email: '<EMAIL>',
            website: 'www.hikvision.com',
            address: 'القاهرة، مصر'
        }
    },
    dahua: {
        name: 'Dahua Technology',
        tagline: 'حلول أمنية شاملة مع تقنيات متقدمة للمراقبة والحماية الذكية',
        logo: 'dahua-logo',
        color: '#ff6600',
        founded: '2001',
        countries: '180+',
        certifications: '40+',
        overview: `داهوا تكنولوجي (Dahua Technology) هي شركة صينية رائدة عالمياً في مجال حلول الأمان والمراقبة الذكية. تأسست عام 2001 في هانغتشو وأصبحت ثاني أكبر مصنع لكاميرات المراقبة في العالم.

تركز داهوا على تطوير تقنيات الذكاء الاصطناعي وإنترنت الأشياء في مجال الأمان. الشركة معروفة بتقنية WizSense المتطورة التي تجمع بين الذكاء الاصطناعي وتحليل البيانات الضخمة لتقديم حلول أمنية ذكية.

تخدم داهوا أكثر من 180 دولة وتوظف أكثر من 26,000 موظف حول العالم. الشركة تستثمر بكثافة في البحث والتطوير وتحمل أكثر من 1,200 براءة اختراع في مجال التكنولوجيا الأمنية.`,
        products: [
            { name: 'تقنية WizSense AI', icon: 'fas fa-magic', description: 'ذكاء اصطناعي متطور لتحليل الفيديو والتعرف على الأنماط' },
            { name: 'كاميرات TiOC', icon: 'fas fa-video', description: 'كاميرات ثلاثية في واحدة: مراقبة، إنذار، وردع' },
            { name: 'تقنية Full-color', icon: 'fas fa-palette', description: 'رؤية ملونة كاملة في الإضاءة المنخفضة' },
            { name: 'أنظمة HDCVI', icon: 'fas fa-broadcast-tower', description: 'تقنية نقل فيديو عالي الدقة عبر الكابلات المحورية' },
            { name: 'حلول التحكم بالوصول', icon: 'fas fa-key', description: 'أنظمة تحكم ذكية بالوصول مع تقنيات بيومترية' },
            { name: 'منصة SmartPSS', icon: 'fas fa-desktop', description: 'برنامج إدارة شامل للأنظمة الأمنية' }
        ],
        features: [
            { title: 'ذكاء اصطناعي WizSense', icon: 'fas fa-brain', description: 'تقنيات AI متقدمة لتحليل السلوك والكشف الذكي' },
            { title: 'دقة عالية 4K+', icon: 'fas fa-eye', description: 'دقة فائقة تصل إلى 4K مع تقنيات تحسين الصورة' },
            { title: 'حلول متكاملة', icon: 'fas fa-puzzle-piece', description: 'نظام بيئي متكامل من الأجهزة والبرمجيات' },
            { title: 'أمان سيبراني', icon: 'fas fa-shield-alt', description: 'حماية متقدمة ضد التهديدات الإلكترونية' },
            { title: 'سهولة الإدارة', icon: 'fas fa-cogs', description: 'واجهات سهلة الاستخدام وإدارة مركزية' },
            { title: 'دعم فني عالمي', icon: 'fas fa-headset', description: 'شبكة دعم فني في أكثر من 180 دولة' }
        ],
        contact: {
            phone: '01002289401',
            email: '<EMAIL>',
            website: 'www.dahuatech.com',
            address: 'القاهرة، مصر'
        }
    },
    axis: {
        name: 'Axis Communications',
        tagline: 'رائدة في كاميرات الشبكة والحلول الأمنية المتطورة من السويد',
        logo: 'axis-logo',
        color: '#00a651',
        founded: '1984',
        countries: '50+',
        certifications: '30+',
        overview: `أكسيس كوميونيكيشنز (Axis Communications) هي الشركة الرائدة عالمياً في مجال كاميرات الشبكة والحلول الأمنية المتطورة. تأسست عام 1984 في لوند، السويد، وهي رائدة في تطوير أول كاميرا IP في العالم عام 1996.

تركز أكسيس على الابتكار والجودة العالية، وتقدم حلول متكاملة للمراقبة والأمان. الشركة معروفة بموثوقيتها العالية وتقنياتها المتطورة في مجال التحليلات والذكاء الاصطناعي.

تخدم أكسيس أكثر من 50 دولة حول العالم وتوظف أكثر من 4,000 موظف. الشركة تستثمر بكثافة في البحث والتطوير وتحمل أكثر من 500 براءة اختراع في مجال تقنيات الشبكة والمراقبة.`,
        products: [
            { name: 'كاميرات الشبكة IP', icon: 'fas fa-network-wired', description: 'كاميرات IP متطورة مع جودة فائقة وموثوقية عالية' },
            { name: 'تحليلات الفيديو', icon: 'fas fa-chart-line', description: 'برامج تحليل متقدمة مع ذكاء اصطناعي مدمج' },
            { name: 'كاميرات PTZ', icon: 'fas fa-arrows-alt', description: 'كاميرات متحركة بتحكم دقيق وزووم بصري عالي' },
            { name: 'أنظمة الصوت الشبكية', icon: 'fas fa-volume-up', description: 'حلول صوتية شبكية للإعلانات والطوارئ' },
            { name: 'تحكم الوصول', icon: 'fas fa-key', description: 'أنظمة تحكم متطورة بالوصول والأبواب' },
            { name: 'برنامج AXIS Camera Station', icon: 'fas fa-desktop', description: 'برنامج إدارة شامل للمراقبة والتسجيل' }
        ],
        features: [
            { title: 'موثوقية سويدية', icon: 'fas fa-check-circle', description: 'جودة وموثوقية عالية بمعايير سويدية صارمة' },
            { title: 'ابتكار مستمر', icon: 'fas fa-lightbulb', description: 'رائدة في الابتكار مع أول كاميرا IP في العالم' },
            { title: 'أمان سيبراني', icon: 'fas fa-shield-alt', description: 'حماية متقدمة مع تشفير من النهاية للنهاية' },
            { title: 'سهولة التكامل', icon: 'fas fa-puzzle-piece', description: 'تكامل سهل مع الأنظمة الموجودة' },
            { title: 'دعم فني ممتاز', icon: 'fas fa-headset', description: 'دعم فني عالمي على مدار الساعة' },
            { title: 'استدامة بيئية', icon: 'fas fa-leaf', description: 'التزام بالاستدامة والمسؤولية البيئية' }
        ],
        contact: {
            phone: '01002289401',
            email: '<EMAIL>',
            website: 'www.axis.com',
            address: 'القاهرة، مصر'
        }
    },
    bosch: {
        name: 'Bosch Security Systems',
        tagline: 'حلول أمنية موثوقة مع تقنيات ألمانية عالية الجودة والموثوقية',
        logo: 'bosch-logo',
        color: '#e60000',
        founded: '1886',
        countries: '60+',
        certifications: '100+',
        overview: `بوش سيكيوريتي سيستمز (Bosch Security Systems) هي قسم من مجموعة بوش الألمانية العريقة التي تأسست عام 1886. تعتبر بوش من الرواد العالميين في مجال التكنولوجيا والخدمات، وقسم الأمان يقدم حلول أمنية متطورة وموثوقة.

تتميز بوش بالجودة الألمانية العالية والابتكار المستمر على مدى أكثر من 130 عاماً. الشركة تقدم مجموعة واسعة من المنتجات الأمنية من كاميرات المراقبة إلى أنظمة الإنذار وحلول الاتصالات المتطورة.

تخدم بوش أكثر من 60 دولة حول العالم وتوظف أكثر من 400,000 موظف عالمياً. الشركة تستثمر أكثر من 7 مليار يورو سنوياً في البحث والتطوير وتحمل أكثر من 59,000 براءة اختراع.`,
        products: [
            { name: 'كاميرات FLEXIDOME', icon: 'fas fa-video', description: 'كاميرات قبة متطورة مع تقنيات ألمانية عالية الجودة' },
            { name: 'كاميرات DINION', icon: 'fas fa-camera', description: 'كاميرات صندوقية احترافية للتطبيقات المتخصصة' },
            { name: 'أنظمة إنذار الحريق', icon: 'fas fa-fire', description: 'أنظمة كشف وإنذار الحريق المتطورة' },
            { name: 'أنظمة الاتصالات', icon: 'fas fa-broadcast-tower', description: 'حلول اتصالات متقدمة للطوارئ والإعلانات' },
            { name: 'تحكم الوصول', icon: 'fas fa-key', description: 'أنظمة تحكم متطورة بالوصول والهوية' },
            { name: 'برنامج BVMS', icon: 'fas fa-desktop', description: 'نظام إدارة الفيديو الشامل من بوش' }
        ],
        features: [
            { title: 'جودة ألمانية أصيلة', icon: 'fas fa-award', description: 'معايير جودة ألمانية صارمة منذ أكثر من 130 عاماً' },
            { title: 'موثوقية استثنائية', icon: 'fas fa-shield-check', description: 'أداء موثوق في أقسى الظروف البيئية' },
            { title: 'ابتكار مستمر', icon: 'fas fa-cogs', description: 'استثمار ضخم في البحث والتطوير' },
            { title: 'حلول متكاملة', icon: 'fas fa-layer-group', description: 'نظام بيئي متكامل من الحلول الأمنية' },
            { title: 'دعم عالمي', icon: 'fas fa-globe', description: 'شبكة دعم وخدمة عالمية شاملة' },
            { title: 'استدامة بيئية', icon: 'fas fa-leaf', description: 'التزام قوي بالاستدامة والمسؤولية البيئية' }
        ],
        contact: {
            phone: '01002289401',
            email: '<EMAIL>',
            website: 'www.boschsecurity.com',
            address: 'القاهرة، مصر'
        }
    },
    hanwha: {
        name: 'Hanwha Techwin',
        tagline: 'تقنيات مراقبة متطورة من كوريا الجنوبية مع حلول ذكية مبتكرة',
        logo: 'hanwha-logo',
        color: '#003d82',
        founded: '1977',
        countries: '90+',
        certifications: '25+',
        overview: `هانوا تكوين (Hanwha Techwin) هي شركة كورية جنوبية رائدة في مجال تقنيات المراقبة والأمان. تأسست عام 1977 كجزء من مجموعة هانوا، وأصبحت من أكبر مصنعي كاميرات المراقبة في العالم.

تتميز هانوا بتقنية WiseNet المتطورة التي تجمع بين الذكاء الاصطناعي وتقنيات الشبكة المتقدمة. الشركة معروفة بكاميراتها متعددة الاستشعار وحلولها المبتكرة للمراقبة الذكية.

تخدم هانوا أكثر من 90 دولة حول العالم وتوظف أكثر من 3,000 موظف متخصص. الشركة تستثمر بكثافة في البحث والتطوير وتحمل أكثر من 300 براءة اختراع في مجال تقنيات المراقبة.`,
        products: [
            { name: 'تقنية WiseNet X', icon: 'fas fa-microchip', description: 'معالجات متطورة مع ذكاء اصطناعي مدمج' },
            { name: 'كاميرات متعددة الاستشعار', icon: 'fas fa-eye', description: 'كاميرات بعدة عدسات لتغطية شاملة' },
            { name: 'تقنية WiseStream', icon: 'fas fa-stream', description: 'ضغط ذكي للفيديو مع توفير النطاق الترددي' },
            { name: 'كاميرات مقاومة الانفجار', icon: 'fas fa-bomb', description: 'كاميرات متخصصة للبيئات الخطرة' },
            { name: 'حلول التحليلات', icon: 'fas fa-chart-bar', description: 'تحليلات فيديو متقدمة مع AI' },
            { name: 'برنامج SSM', icon: 'fas fa-desktop', description: 'نظام إدارة المراقبة الذكي' }
        ],
        features: [
            { title: 'تقنية كورية متطورة', icon: 'fas fa-rocket', description: 'ابتكارات تقنية من كوريا الجنوبية المتقدمة' },
            { title: 'ذكاء اصطناعي مدمج', icon: 'fas fa-brain', description: 'معالجة ذكية مدمجة في الكاميرات' },
            { title: 'كفاءة في الطاقة', icon: 'fas fa-battery-full', description: 'تصميم موفر للطاقة مع أداء عالي' },
            { title: 'مقاومة قاسية', icon: 'fas fa-shield-alt', description: 'مقاومة للظروف البيئية القاسية' },
            { title: 'سهولة التركيب', icon: 'fas fa-tools', description: 'تصميم سهل التركيب والصيانة' },
            { title: 'دعم تقني متخصص', icon: 'fas fa-headset', description: 'دعم فني من خبراء كوريين' }
        ],
        contact: {
            phone: '01002289401',
            email: '<EMAIL>',
            website: 'www.hanwha-security.com',
            address: 'القاهرة، مصر'
        }
    },
    avigilon: {
        name: 'Avigilon',
        tagline: 'حلول مراقبة فيديو متطورة مع تقنيات الذكاء الاصطناعي من كندا',
        logo: 'avigilon-logo',
        color: '#1e3a8a',
        founded: '2004',
        countries: '100+',
        certifications: '20+',
        overview: `أفيجيلون (Avigilon) هي شركة كندية رائدة في مجال حلول مراقبة الفيديو المتطورة. تأسست عام 2004 في فانكوفر، كندا، وأصبحت جزءاً من مجموعة موتورولا سوليوشنز عام 2018.

تتميز أفيجيلون بتقنيات الذكاء الاصطناعي المتطورة وكاميراتها عالية الدقة. الشركة معروفة بنظام ACC (Avigilon Control Center) الذي يوفر إدارة شاملة للمراقبة مع تحليلات متقدمة.

تخدم أفيجيلون أكثر من 100 دولة حول العالم وتركز على التطبيقات الحرجة مثل المطارات، المستشفيات، والمؤسسات الحكومية. الشركة تستثمر بكثافة في تقنيات الذكاء الاصطناعي والتعلم الآلي.`,
        products: [
            { name: 'نظام ACC', icon: 'fas fa-desktop', description: 'نظام التحكم المركزي الشامل من أفيجيلون' },
            { name: 'كاميرات H4 HD', icon: 'fas fa-video', description: 'كاميرات عالية الدقة مع تقنيات متطورة' },
            { name: 'تحليلات الفيديو', icon: 'fas fa-chart-line', description: 'تحليلات ذكية للسلوك والأحداث' },
            { name: 'كاميرات متعددة الاستشعار', icon: 'fas fa-eye', description: 'كاميرات بعدة عدسات لتغطية واسعة' },
            { name: 'حلول التعرف على الوجوه', icon: 'fas fa-user-check', description: 'تقنيات متطورة للتعرف على الهوية' },
            { name: 'تطبيقات الهاتف المحمول', icon: 'fas fa-mobile-alt', description: 'تطبيقات للمراقبة عن بعد' }
        ],
        features: [
            { title: 'ذكاء اصطناعي متقدم', icon: 'fas fa-robot', description: 'تقنيات AI كندية متطورة للتحليل الذكي' },
            { title: 'دقة فائقة', icon: 'fas fa-eye', description: 'كاميرات عالية الدقة تصل إلى 30 ميجابكسل' },
            { title: 'سهولة الاستخدام', icon: 'fas fa-user-friendly', description: 'واجهات بديهية وسهلة الاستخدام' },
            { title: 'موثوقية عالية', icon: 'fas fa-shield-check', description: 'أداء موثوق في التطبيقات الحرجة' },
            { title: 'تكامل مرن', icon: 'fas fa-puzzle-piece', description: 'تكامل سهل مع الأنظمة الموجودة' },
            { title: 'دعم عالمي', icon: 'fas fa-globe', description: 'شبكة دعم عالمية من موتورولا' }
        ],
        contact: {
            phone: '01002289401',
            email: '<EMAIL>',
            website: 'www.avigilon.com',
            address: 'القاهرة، مصر'
        }
    }
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    // Check authentication first
    if (!checkAuthentication()) {
        return; // Will redirect to login
    }

    // Initialize store if authenticated
    initializeStore();
});

// Check if user is authenticated
function checkAuthentication() {
    const currentUser = localStorage.getItem('egbank_current_user');
    const loginTime = localStorage.getItem('egbank_login_time');

    if (!currentUser || !loginTime) {
        redirectToLogin();
        return false;
    }

    // Check if login is still valid (24 hours)
    const loginDate = new Date(loginTime);
    const now = new Date();
    const hoursDiff = (now - loginDate) / (1000 * 60 * 60);

    if (hoursDiff >= 24) {
        // Session expired
        localStorage.removeItem('egbank_current_user');
        localStorage.removeItem('egbank_login_time');
        showNotification('انتهت صلاحية جلسة العمل. يرجى تسجيل الدخول مرة أخرى', 'warning');
        setTimeout(() => {
            redirectToLogin();
        }, 2000);
        return false;
    }

    // Update user info in header
    updateUserInfo(currentUser);
    return true;
}

// Initialize store functionality
function initializeStore() {
    loadProducts();
    updateWishlistCount();

    // Add smooth scrolling
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Update user info in header
function updateUserInfo(username) {
    const users = JSON.parse(localStorage.getItem('egbank_users')) || {};
    const user = users[username];

    if (user) {
        // Add user info to header
        const headerActions = document.querySelector('.header-actions');
        const userInfo = document.createElement('div');
        userInfo.className = 'user-info';
        userInfo.innerHTML = `
            <div class="user-dropdown">
                <div class="user-avatar" onclick="toggleUserMenu()">
                    <i class="fas fa-user"></i>
                    <span>${user.fullName || username}</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="user-menu" id="userMenu">
                    <div class="user-menu-header">
                        <strong>${user.fullName || username}</strong>
                        <small>${user.email}</small>
                    </div>
                    <div class="user-menu-divider"></div>
                    <a href="#" onclick="showProfile()">
                        <i class="fas fa-user-cog"></i>
                        الملف الشخصي
                    </a>
                    <a href="#" onclick="showOrders()">
                        <i class="fas fa-shopping-bag"></i>
                        طلباتي
                    </a>
                    <a href="#" onclick="showSettings()">
                        <i class="fas fa-cog"></i>
                        الإعدادات
                    </a>
                    <div class="user-menu-divider"></div>
                    <a href="#" onclick="logout()" class="logout-link">
                        <i class="fas fa-sign-out-alt"></i>
                        تسجيل الخروج
                    </a>
                </div>
            </div>
        `;

        // Insert before wishlist
        headerActions.insertBefore(userInfo, headerActions.firstChild);
    }
}

// Redirect to login page
function redirectToLogin() {
    window.location.href = 'login.html';
}

// Logout function
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        localStorage.removeItem('egbank_current_user');
        localStorage.removeItem('egbank_login_time');
        showNotification('تم تسجيل الخروج بنجاح', 'success');
        setTimeout(() => {
            redirectToLogin();
        }, 1500);
    }
}

// Toggle user menu
function toggleUserMenu() {
    const userMenu = document.getElementById('userMenu');
    userMenu.style.display = userMenu.style.display === 'block' ? 'none' : 'block';
}

// Close user menu when clicking outside
document.addEventListener('click', function(event) {
    const userDropdown = document.querySelector('.user-dropdown');
    const userMenu = document.getElementById('userMenu');

    if (userMenu && userDropdown && !userDropdown.contains(event.target)) {
        userMenu.style.display = 'none';
    }

    // Close brand modal when clicking outside
    const brandModal = document.getElementById('brandModal');
    if (brandModal && event.target === brandModal) {
        closeBrandModal();
    }
});

// Placeholder functions for user menu items
function showProfile() {
    showNotification('صفحة الملف الشخصي قيد التطوير', 'info');
    toggleUserMenu();
}

function showOrders() {
    showNotification('صفحة الطلبات قيد التطوير', 'info');
    toggleUserMenu();
}

function showSettings() {
    showNotification('صفحة الإعدادات قيد التطوير', 'info');
    toggleUserMenu();
}

// Brand Functions
function openBrandDetails(brandId) {
    currentBrand = securityBrands[brandId];
    if (!currentBrand) return;

    // Update modal content
    updateBrandModal();

    // Show modal
    document.getElementById('brandModal').style.display = 'block';
    document.body.style.overflow = 'hidden';
}

function updateBrandModal() {
    if (!currentBrand) return;

    // Update header logo based on brand
    let logoHTML = '';
    switch(currentBrand.logo) {
        case 'hikvision-logo':
            logoHTML = `<div class="hikvision-icon" style="color: white; font-size: 24px; font-weight: bold;">H</div>`;
            break;
        case 'dahua-logo':
            logoHTML = `<div class="dahua-icon" style="color: white; font-size: 18px; font-weight: bold;">大华</div>`;
            break;
        case 'axis-logo':
            logoHTML = `<div class="axis-icon" style="color: white; font-size: 12px; font-weight: bold; letter-spacing: 1px;">AXIS</div>`;
            break;
        case 'bosch-logo':
            logoHTML = `<div class="bosch-icon" style="color: white; font-size: 11px; font-weight: bold; letter-spacing: 1px;">BOSCH</div>`;
            break;
        case 'hanwha-logo':
            logoHTML = `<div class="hanwha-icon" style="color: white; font-size: 18px; font-weight: bold;">한화</div>`;
            break;
        case 'avigilon-logo':
            logoHTML = `
                <div style="display: flex; flex-direction: column; align-items: center; gap: 5px;">
                    <div style="width: 30px; height: 30px; border: 2px solid white; border-radius: 50%; position: relative;">
                        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 15px; height: 15px; background: white; border-radius: 50%;"></div>
                    </div>
                    <span style="font-size: 10px; font-weight: bold; color: white; letter-spacing: 1px;">AVIGILON</span>
                </div>
            `;
            break;
        default:
            logoHTML = `<i class="${currentBrand.logo}" style="color: white; font-size: 32px;"></i>`;
    }

    document.getElementById('brandModalLogo').innerHTML = logoHTML;
    document.getElementById('brandModalName').textContent = currentBrand.name;
    document.getElementById('brandModalTagline').textContent = currentBrand.tagline;

    // Update header background
    const header = document.querySelector('.brand-modal-header');
    header.style.background = `linear-gradient(135deg, ${currentBrand.color}, ${adjustColor(currentBrand.color, -20)})`;

    // Update overview tab
    document.getElementById('brandOverviewText').innerHTML = currentBrand.overview.replace(/\n/g, '<br><br>');
    document.getElementById('brandFoundedYear').textContent = currentBrand.founded;
    document.getElementById('brandCountries').textContent = currentBrand.countries;
    document.getElementById('brandCertifications').textContent = currentBrand.certifications;

    // Update products tab
    updateBrandProducts();

    // Update features tab
    updateBrandFeatures();

    // Update contact tab
    updateBrandContact();
}

function updateBrandProducts() {
    const productsList = document.getElementById('brandProductsList');
    productsList.innerHTML = '';

    currentBrand.products.forEach(product => {
        const productItem = document.createElement('div');
        productItem.className = 'product-item';
        productItem.innerHTML = `
            <i class="${product.icon}"></i>
            <h4>${product.name}</h4>
            <p>${product.description}</p>
        `;
        productsList.appendChild(productItem);
    });
}

function updateBrandFeatures() {
    const featuresList = document.getElementById('brandFeaturesList');
    featuresList.innerHTML = '';

    currentBrand.features.forEach(feature => {
        const featureItem = document.createElement('div');
        featureItem.className = 'feature-item';
        featureItem.innerHTML = `
            <i class="${feature.icon}"></i>
            <div>
                <h4>${feature.title}</h4>
                <p>${feature.description}</p>
            </div>
        `;
        featuresList.appendChild(featureItem);
    });
}

function updateBrandContact() {
    const contactInfo = document.getElementById('brandContactInfo');
    contactInfo.innerHTML = `
        <div class="contact-item contact-item-interactive">
            <i class="fas fa-phone"></i>
            <h4>الهاتف</h4>
            <p><a href="tel:+2${currentBrand.contact.phone}" class="contact-link phone-link">${currentBrand.contact.phone}</a></p>
        </div>
        <div class="contact-item contact-item-interactive">
            <i class="fas fa-envelope"></i>
            <h4>البريد الإلكتروني</h4>
            <p><a href="mailto:${currentBrand.contact.email}" class="contact-link email-link">${currentBrand.contact.email}</a></p>
        </div>
        <div class="contact-item contact-item-interactive">
            <i class="fas fa-globe"></i>
            <h4>الموقع الإلكتروني</h4>
            <p><a href="https://${currentBrand.contact.website}" target="_blank" class="contact-link website-link">${currentBrand.contact.website}</a></p>
        </div>
        <div class="contact-item contact-item-interactive">
            <i class="fas fa-map-marker-alt"></i>
            <h4>العنوان</h4>
            <p><a href="https://maps.google.com/?q=${encodeURIComponent(currentBrand.contact.address)}" target="_blank" class="contact-link location-link">${currentBrand.contact.address}</a></p>
        </div>
    `;
}

function showBrandTab(tabName) {
    // Hide all tabs
    document.querySelectorAll('.tab-panel').forEach(panel => {
        panel.classList.remove('active');
    });

    // Remove active class from all buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // Show selected tab
    document.getElementById(tabName).classList.add('active');

    // Add active class to clicked button
    event.target.classList.add('active');
}

function closeBrandModal() {
    document.getElementById('brandModal').style.display = 'none';
    document.body.style.overflow = 'auto';
    currentBrand = null;
}

function requestQuote() {
    if (!currentBrand) return;

    showNotification(`تم إرسال طلب عرض سعر لمنتجات ${currentBrand.name}`, 'success');

    // Here you would typically send the request to your backend
    console.log(`Quote requested for ${currentBrand.name}`);
}

function downloadCatalog() {
    if (!currentBrand) return;

    showNotification(`جاري تحميل كتالوج ${currentBrand.name}...`, 'info');

    // Here you would typically trigger a download
    console.log(`Catalog download for ${currentBrand.name}`);
}

// Helper function to adjust color brightness
function adjustColor(color, amount) {
    const usePound = color[0] === '#';
    const col = usePound ? color.slice(1) : color;
    const num = parseInt(col, 16);
    let r = (num >> 16) + amount;
    let g = (num >> 8 & 0x00FF) + amount;
    let b = (num & 0x0000FF) + amount;
    r = r > 255 ? 255 : r < 0 ? 0 : r;
    g = g > 255 ? 255 : g < 0 ? 0 : g;
    b = b > 255 ? 255 : b < 0 ? 0 : b;
    return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0');
}

// Show specific section
function showSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.content-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // Show selected section
    document.getElementById(sectionId).classList.add('active');
    
    // Update navigation
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    event.target.classList.add('active');
}

// Load and display products
function loadProducts(filter = 'all') {
    const productsGrid = document.getElementById('productsGrid');
    productsGrid.innerHTML = '';
    
    const filteredProducts = filter === 'all' ? products : products.filter(product => product.category === filter);
    
    filteredProducts.forEach(product => {
        const productCard = createProductCard(product);
        productsGrid.appendChild(productCard);
    });
    
    // Add animation
    setTimeout(() => {
        document.querySelectorAll('.product-card').forEach((card, index) => {
            card.style.animation = `fadeInUp 0.6s ease ${index * 0.1}s both`;
        });
    }, 100);
}

// Create product card element
function createProductCard(product) {
    const card = document.createElement('div');
    card.className = 'product-card';
    card.onclick = () => openProductModal(product);
    
    card.innerHTML = `
        <div class="product-image">
            <img src="${product.image}" alt="${product.title}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
            <div style="display:none; width:100%; height:100%; background:linear-gradient(45deg, #667eea, #764ba2); align-items:center; justify-content:center; color:white; font-weight:600;">
                ${product.title}
            </div>
        </div>
        <div class="product-info">
            <div class="product-title">${product.title}</div>
            <div class="product-description">${product.description}</div>
            <div class="product-price">${product.price}</div>
            <div class="product-actions">
                <button class="btn-primary" onclick="event.stopPropagation(); addToCart(${product.id})">
                    <i class="fas fa-shopping-cart"></i> إضافة للسلة
                </button>
                <button class="btn-secondary" onclick="event.stopPropagation(); toggleWishlistItem(${product.id})">
                    <i class="fas fa-heart ${wishlistItems.includes(product.id) ? 'fas' : 'far'}"></i>
                </button>
            </div>
        </div>
    `;
    
    return card;
}

// Filter products
function filterProducts(category) {
    currentFilter = category;
    loadProducts(category);
    
    // Update filter buttons
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    event.target.classList.add('active');
    
    // Show notification
    const categoryNames = {
        'all': 'جميع المنتجات',
        'infographics': 'الإنفوجرافيك',
        'presentations': 'العروض التقديمية',
        'business': 'الأعمال',
        'marketing': 'التسويق'
    };
    
    showNotification(`تم تصفية المنتجات: ${categoryNames[category]}`, 'info');
}

// Search functionality
function performSearch() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
    
    if (!searchTerm) {
        loadProducts(currentFilter);
        return;
    }
    
    const filteredProducts = products.filter(product => 
        product.title.toLowerCase().includes(searchTerm) ||
        product.description.toLowerCase().includes(searchTerm)
    );
    
    const productsGrid = document.getElementById('productsGrid');
    productsGrid.innerHTML = '';
    
    if (filteredProducts.length === 0) {
        productsGrid.innerHTML = `
            <div style="grid-column: 1/-1; text-align: center; padding: 40px; color: #666;">
                <i class="fas fa-search" style="font-size: 48px; margin-bottom: 20px; opacity: 0.5;"></i>
                <h3>لم يتم العثور على منتجات</h3>
                <p>جرب البحث بكلمات مختلفة</p>
            </div>
        `;
    } else {
        filteredProducts.forEach(product => {
            const productCard = createProductCard(product);
            productsGrid.appendChild(productCard);
        });
        
        showNotification(`تم العثور على ${filteredProducts.length} منتج`, 'success');
    }
}

// Add Enter key support for search
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
});

// Scroll to products section
function scrollToProducts() {
    document.getElementById('products-section').scrollIntoView({
        behavior: 'smooth'
    });
}

// Open product modal
function openProductModal(product) {
    currentProduct = product;
    
    document.getElementById('modalProductImage').src = product.image;
    document.getElementById('modalProductTitle').textContent = product.title;
    document.getElementById('modalProductDescription').textContent = product.description;
    document.getElementById('modalProductPrice').textContent = product.price;
    
    document.getElementById('productModal').style.display = 'block';
    document.body.style.overflow = 'hidden';
}

// Close product modal
function closeProductModal() {
    document.getElementById('productModal').style.display = 'none';
    document.body.style.overflow = 'auto';
    currentProduct = null;
}

// Add to cart
function addToCart(productId = null) {
    const id = productId || (currentProduct ? currentProduct.id : null);
    if (!id) return;
    
    const product = products.find(p => p.id === id);
    if (product) {
        showNotification(`تم إضافة "${product.title}" إلى السلة`, 'success');
        
        // Close modal if open
        if (document.getElementById('productModal').style.display === 'block') {
            closeProductModal();
        }
    }
}

// Toggle wishlist item
function toggleWishlistItem(productId) {
    const index = wishlistItems.indexOf(productId);
    const product = products.find(p => p.id === productId);

    if (index > -1) {
        wishlistItems.splice(index, 1);
        showNotification(`تم إزالة "${product.title}" من المفضلة`, 'info');
    } else {
        wishlistItems.push(productId);
        showNotification(`تم إضافة "${product.title}" إلى المفضلة`, 'success');
    }

    localStorage.setItem('wishlistItems', JSON.stringify(wishlistItems));
    updateWishlistCount();

    // Update heart icons
    updateWishlistIcons();

    // If wishlist modal is open, refresh it
    const wishlistModal = document.getElementById('wishlistModal');
    if (wishlistModal.style.display === 'block') {
        // Refresh wishlist modal content
        setTimeout(() => {
            const wishlistItemsContainer = document.getElementById('wishlistItems');
            wishlistItemsContainer.innerHTML = '';

            if (wishlistItems.length === 0) {
                wishlistItemsContainer.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <i class="fas fa-heart" style="font-size: 48px; margin-bottom: 20px; opacity: 0.5;"></i>
                        <h3>قائمة المفضلة فارغة</h3>
                        <p>أضف بعض المنتجات إلى قائمة المفضلة</p>
                    </div>
                `;
            } else {
                wishlistItems.forEach(id => {
                    const prod = products.find(p => p.id === id);
                    if (prod) {
                        const wishlistItem = document.createElement('div');
                        wishlistItem.className = 'wishlist-item';
                        wishlistItem.id = `wishlist-item-${prod.id}`;
                        wishlistItem.style.cssText = `
                            display: flex;
                            align-items: center;
                            gap: 15px;
                            padding: 15px;
                            border: 1px solid #e9ecef;
                            border-radius: 10px;
                            margin-bottom: 15px;
                            transition: all 0.3s ease;
                        `;

                        wishlistItem.innerHTML = `
                            <img src="${prod.image}" alt="${prod.title}" style="width: 80px; height: 60px; object-fit: cover; border-radius: 5px;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div style="display:none; width:80px; height:60px; background:linear-gradient(45deg, #667eea, #764ba2); align-items:center; justify-content:center; color:white; font-size:12px; border-radius:5px; text-align:center;">
                                ${prod.title.substring(0, 10)}...
                            </div>
                            <div style="flex: 1;">
                                <h4 style="margin-bottom: 5px; color: #333;">${prod.title}</h4>
                                <p style="color: #666; font-size: 14px;">${prod.price}</p>
                            </div>
                            <div style="display: flex; gap: 10px;">
                                <button onclick="addToCartFromWishlist(${prod.id})" style="background: #7c4dff; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer; transition: background 0.3s ease;" onmouseover="this.style.background='#6a3de8'" onmouseout="this.style.background='#7c4dff'">
                                    <i class="fas fa-shopping-cart"></i>
                                </button>
                                <button onclick="removeFromWishlist(${prod.id})" style="background: #ff4757; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer; transition: background 0.3s ease;" onmouseover="this.style.background='#ff3742'" onmouseout="this.style.background='#ff4757'">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        `;

                        wishlistItemsContainer.appendChild(wishlistItem);
                    }
                });
            }
        }, 100);
    }
}

// Update wishlist count
function updateWishlistCount() {
    document.getElementById('wishlistCount').textContent = wishlistItems.length;
}

// Update wishlist icons
function updateWishlistIcons() {
    document.querySelectorAll('.product-card').forEach(card => {
        const heartIcon = card.querySelector('.btn-secondary i');
        const productId = parseInt(card.querySelector('.btn-secondary').onclick.toString().match(/\d+/)[0]);
        
        if (wishlistItems.includes(productId)) {
            heartIcon.className = 'fas fa-heart';
        } else {
            heartIcon.className = 'far fa-heart';
        }
    });
}

// Toggle wishlist modal
function toggleWishlist() {
    const modal = document.getElementById('wishlistModal');
    const wishlistItemsContainer = document.getElementById('wishlistItems');

    // Clear previous content
    wishlistItemsContainer.innerHTML = '';

    if (wishlistItems.length === 0) {
        wishlistItemsContainer.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #666;">
                <i class="fas fa-heart" style="font-size: 48px; margin-bottom: 20px; opacity: 0.5;"></i>
                <h3>قائمة المفضلة فارغة</h3>
                <p>أضف بعض المنتجات إلى قائمة المفضلة</p>
            </div>
        `;
    } else {
        wishlistItems.forEach(productId => {
            const product = products.find(p => p.id === productId);
            if (product) {
                const wishlistItem = document.createElement('div');
                wishlistItem.className = 'wishlist-item';
                wishlistItem.id = `wishlist-item-${product.id}`;
                wishlistItem.style.cssText = `
                    display: flex;
                    align-items: center;
                    gap: 15px;
                    padding: 15px;
                    border: 1px solid #e9ecef;
                    border-radius: 10px;
                    margin-bottom: 15px;
                    transition: all 0.3s ease;
                `;

                wishlistItem.innerHTML = `
                    <img src="${product.image}" alt="${product.title}" style="width: 80px; height: 60px; object-fit: cover; border-radius: 5px;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div style="display:none; width:80px; height:60px; background:linear-gradient(45deg, #667eea, #764ba2); align-items:center; justify-content:center; color:white; font-size:12px; border-radius:5px; text-align:center;">
                        ${product.title.substring(0, 10)}...
                    </div>
                    <div style="flex: 1;">
                        <h4 style="margin-bottom: 5px; color: #333;">${product.title}</h4>
                        <p style="color: #666; font-size: 14px;">${product.price}</p>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <button onclick="addToCartFromWishlist(${product.id})" style="background: #7c4dff; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer; transition: background 0.3s ease;" onmouseover="this.style.background='#6a3de8'" onmouseout="this.style.background='#7c4dff'">
                            <i class="fas fa-shopping-cart"></i>
                        </button>
                        <button onclick="removeFromWishlist(${product.id})" style="background: #ff4757; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer; transition: background 0.3s ease;" onmouseover="this.style.background='#ff3742'" onmouseout="this.style.background='#ff4757'">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                `;

                wishlistItemsContainer.appendChild(wishlistItem);
            }
        });
    }

    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';
}

// Close wishlist modal
function closeWishlistModal() {
    document.getElementById('wishlistModal').style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Submit contact form
function submitContactForm(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const name = event.target.querySelector('input[type="text"]').value;
    const email = event.target.querySelector('input[type="email"]').value;
    const message = event.target.querySelector('textarea').value;
    
    // Simulate form submission
    showNotification('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.', 'success');
    
    // Reset form
    event.target.reset();
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    
    let backgroundColor;
    switch(type) {
        case 'success':
            backgroundColor = '#4CAF50';
            break;
        case 'error':
            backgroundColor = '#f44336';
            break;
        case 'warning':
            backgroundColor = '#ff9800';
            break;
        case 'info':
        default:
            backgroundColor = '#2196F3';
            break;
    }
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${backgroundColor};
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        z-index: 1001;
        font-weight: 600;
        animation: slideInRight 0.3s ease;
        max-width: 300px;
        word-wrap: break-word;
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(100px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    @keyframes slideOutRight {
        from {
            opacity: 1;
            transform: translateX(0);
        }
        to {
            opacity: 0;
            transform: translateX(100px);
        }
    }
    
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
`;
document.head.appendChild(style);

// Close modals when clicking outside
window.onclick = function(event) {
    const productModal = document.getElementById('productModal');
    const wishlistModal = document.getElementById('wishlistModal');
    
    if (event.target === productModal) {
        closeProductModal();
    }
    
    if (event.target === wishlistModal) {
        closeWishlistModal();
    }
}

// Add to wishlist from modal
function addToWishlist() {
    if (currentProduct) {
        toggleWishlistItem(currentProduct.id);
    }
}

// Remove from wishlist with animation
function removeFromWishlist(productId) {
    const product = products.find(p => p.id === productId);
    const wishlistItem = document.getElementById(`wishlist-item-${productId}`);

    if (wishlistItem && product) {
        // Add removal animation
        wishlistItem.style.transform = 'translateX(-100%)';
        wishlistItem.style.opacity = '0';

        setTimeout(() => {
            // Remove from array
            const index = wishlistItems.indexOf(productId);
            if (index > -1) {
                wishlistItems.splice(index, 1);
                localStorage.setItem('wishlistItems', JSON.stringify(wishlistItems));
                updateWishlistCount();
                updateWishlistIcons();
            }

            // Remove from DOM
            if (wishlistItem.parentNode) {
                wishlistItem.parentNode.removeChild(wishlistItem);
            }

            // Show notification
            showNotification(`تم حذف "${product.title}" من المفضلة`, 'success');

            // Check if wishlist is empty now
            if (wishlistItems.length === 0) {
                const wishlistItemsContainer = document.getElementById('wishlistItems');
                wishlistItemsContainer.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <i class="fas fa-heart" style="font-size: 48px; margin-bottom: 20px; opacity: 0.5;"></i>
                        <h3>قائمة المفضلة فارغة</h3>
                        <p>أضف بعض المنتجات إلى قائمة المفضلة</p>
                    </div>
                `;
            }
        }, 300);
    }
}

// Add to cart from wishlist
function addToCartFromWishlist(productId) {
    const product = products.find(p => p.id === productId);
    if (product) {
        showNotification(`تم إضافة "${product.title}" إلى السلة`, 'success');

        // Optional: Remove from wishlist after adding to cart
        // removeFromWishlist(productId);
    }
}
