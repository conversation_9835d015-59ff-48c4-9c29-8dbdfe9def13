// Global Variables
let currentFilter = 'all';
let wishlistItems = JSON.parse(localStorage.getItem('wishlistItems')) || [];
let currentProduct = null;

// Sample Products Data
const products = [
    {
        id: 1,
        title: "إنفوجرافيك الأعمال المتقدم",
        description: "مجموعة شاملة من قوالب الإنفوجرافيك للشركات والمؤسسات",
        price: "299 جنيه",
        category: "infographics",
        image: "https://via.placeholder.com/300x200/667eea/ffffff?text=Infographic+1"
    },
    {
        id: 2,
        title: "عرض تقديمي احترافي",
        description: "قالب PowerPoint متميز للعروض التقديمية المهنية",
        price: "199 جنيه",
        category: "presentations",
        image: "https://via.placeholder.com/300x200/764ba2/ffffff?text=Presentation+1"
    },
    {
        id: 3,
        title: "تصميم هوية الشركات",
        description: "حزمة كاملة لتصميم هوية بصرية متكاملة للشركات",
        price: "499 جنيه",
        category: "business",
        image: "https://via.placeholder.com/300x200/f093fb/ffffff?text=Business+1"
    },
    {
        id: 4,
        title: "مواد تسويقية إبداعية",
        description: "مجموعة من التصاميم التسويقية للحملات الإعلانية",
        price: "349 جنيه",
        category: "marketing",
        image: "https://via.placeholder.com/300x200/4facfe/ffffff?text=Marketing+1"
    },
    {
        id: 5,
        title: "إنفوجرافيك البيانات",
        description: "قوالب متخصصة لعرض البيانات والإحصائيات",
        price: "249 جنيه",
        category: "infographics",
        image: "https://via.placeholder.com/300x200/667eea/ffffff?text=Infographic+2"
    },
    {
        id: 6,
        title: "عرض المنتجات",
        description: "قوالب لعرض المنتجات والخدمات بطريقة جذابة",
        price: "179 جنيه",
        category: "presentations",
        image: "https://via.placeholder.com/300x200/764ba2/ffffff?text=Presentation+2"
    },
    {
        id: 7,
        title: "تقارير الأعمال",
        description: "قوالب احترافية لإعداد التقارير السنوية والشهرية",
        price: "399 جنيه",
        category: "business",
        image: "https://via.placeholder.com/300x200/f093fb/ffffff?text=Business+2"
    },
    {
        id: 8,
        title: "حملات وسائل التواصل",
        description: "تصاميم متنوعة لحملات وسائل التواصل الاجتماعي",
        price: "229 جنيه",
        category: "marketing",
        image: "https://via.placeholder.com/300x200/4facfe/ffffff?text=Marketing+2"
    },
    {
        id: 9,
        title: "رسوم بيانية تفاعلية",
        description: "مجموعة من الرسوم البيانية التفاعلية والحديثة",
        price: "319 جنيه",
        category: "infographics",
        image: "https://via.placeholder.com/300x200/667eea/ffffff?text=Infographic+3"
    },
    {
        id: 10,
        title: "عروض المبيعات",
        description: "قوالب مخصصة لعروض المبيعات والعملاء",
        price: "279 جنيه",
        category: "presentations",
        image: "https://via.placeholder.com/300x200/764ba2/ffffff?text=Presentation+3"
    },
    {
        id: 11,
        title: "استراتيجية الأعمال",
        description: "قوالب لعرض استراتيجيات الأعمال والخطط المستقبلية",
        price: "449 جنيه",
        category: "business",
        image: "https://via.placeholder.com/300x200/f093fb/ffffff?text=Business+3"
    },
    {
        id: 12,
        title: "تحليل السوق",
        description: "أدوات بصرية لتحليل السوق والمنافسين",
        price: "369 جنيه",
        category: "marketing",
        image: "https://via.placeholder.com/300x200/4facfe/ffffff?text=Marketing+3"
    }
];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    loadProducts();
    updateWishlistCount();
    
    // Add smooth scrolling
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });
});

// Show specific section
function showSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.content-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // Show selected section
    document.getElementById(sectionId).classList.add('active');
    
    // Update navigation
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    event.target.classList.add('active');
}

// Load and display products
function loadProducts(filter = 'all') {
    const productsGrid = document.getElementById('productsGrid');
    productsGrid.innerHTML = '';
    
    const filteredProducts = filter === 'all' ? products : products.filter(product => product.category === filter);
    
    filteredProducts.forEach(product => {
        const productCard = createProductCard(product);
        productsGrid.appendChild(productCard);
    });
    
    // Add animation
    setTimeout(() => {
        document.querySelectorAll('.product-card').forEach((card, index) => {
            card.style.animation = `fadeInUp 0.6s ease ${index * 0.1}s both`;
        });
    }, 100);
}

// Create product card element
function createProductCard(product) {
    const card = document.createElement('div');
    card.className = 'product-card';
    card.onclick = () => openProductModal(product);
    
    card.innerHTML = `
        <div class="product-image">
            <img src="${product.image}" alt="${product.title}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
            <div style="display:none; width:100%; height:100%; background:linear-gradient(45deg, #667eea, #764ba2); align-items:center; justify-content:center; color:white; font-weight:600;">
                ${product.title}
            </div>
        </div>
        <div class="product-info">
            <div class="product-title">${product.title}</div>
            <div class="product-description">${product.description}</div>
            <div class="product-price">${product.price}</div>
            <div class="product-actions">
                <button class="btn-primary" onclick="event.stopPropagation(); addToCart(${product.id})">
                    <i class="fas fa-shopping-cart"></i> إضافة للسلة
                </button>
                <button class="btn-secondary" onclick="event.stopPropagation(); toggleWishlistItem(${product.id})">
                    <i class="fas fa-heart ${wishlistItems.includes(product.id) ? 'fas' : 'far'}"></i>
                </button>
            </div>
        </div>
    `;
    
    return card;
}

// Filter products
function filterProducts(category) {
    currentFilter = category;
    loadProducts(category);
    
    // Update filter buttons
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    event.target.classList.add('active');
    
    // Show notification
    const categoryNames = {
        'all': 'جميع المنتجات',
        'infographics': 'الإنفوجرافيك',
        'presentations': 'العروض التقديمية',
        'business': 'الأعمال',
        'marketing': 'التسويق'
    };
    
    showNotification(`تم تصفية المنتجات: ${categoryNames[category]}`, 'info');
}

// Search functionality
function performSearch() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
    
    if (!searchTerm) {
        loadProducts(currentFilter);
        return;
    }
    
    const filteredProducts = products.filter(product => 
        product.title.toLowerCase().includes(searchTerm) ||
        product.description.toLowerCase().includes(searchTerm)
    );
    
    const productsGrid = document.getElementById('productsGrid');
    productsGrid.innerHTML = '';
    
    if (filteredProducts.length === 0) {
        productsGrid.innerHTML = `
            <div style="grid-column: 1/-1; text-align: center; padding: 40px; color: #666;">
                <i class="fas fa-search" style="font-size: 48px; margin-bottom: 20px; opacity: 0.5;"></i>
                <h3>لم يتم العثور على منتجات</h3>
                <p>جرب البحث بكلمات مختلفة</p>
            </div>
        `;
    } else {
        filteredProducts.forEach(product => {
            const productCard = createProductCard(product);
            productsGrid.appendChild(productCard);
        });
        
        showNotification(`تم العثور على ${filteredProducts.length} منتج`, 'success');
    }
}

// Add Enter key support for search
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
});

// Scroll to products section
function scrollToProducts() {
    document.getElementById('products-section').scrollIntoView({
        behavior: 'smooth'
    });
}

// Open product modal
function openProductModal(product) {
    currentProduct = product;
    
    document.getElementById('modalProductImage').src = product.image;
    document.getElementById('modalProductTitle').textContent = product.title;
    document.getElementById('modalProductDescription').textContent = product.description;
    document.getElementById('modalProductPrice').textContent = product.price;
    
    document.getElementById('productModal').style.display = 'block';
    document.body.style.overflow = 'hidden';
}

// Close product modal
function closeProductModal() {
    document.getElementById('productModal').style.display = 'none';
    document.body.style.overflow = 'auto';
    currentProduct = null;
}

// Add to cart
function addToCart(productId = null) {
    const id = productId || (currentProduct ? currentProduct.id : null);
    if (!id) return;
    
    const product = products.find(p => p.id === id);
    if (product) {
        showNotification(`تم إضافة "${product.title}" إلى السلة`, 'success');
        
        // Close modal if open
        if (document.getElementById('productModal').style.display === 'block') {
            closeProductModal();
        }
    }
}

// Toggle wishlist item
function toggleWishlistItem(productId) {
    const index = wishlistItems.indexOf(productId);
    const product = products.find(p => p.id === productId);

    if (index > -1) {
        wishlistItems.splice(index, 1);
        showNotification(`تم إزالة "${product.title}" من المفضلة`, 'info');
    } else {
        wishlistItems.push(productId);
        showNotification(`تم إضافة "${product.title}" إلى المفضلة`, 'success');
    }

    localStorage.setItem('wishlistItems', JSON.stringify(wishlistItems));
    updateWishlistCount();

    // Update heart icons
    updateWishlistIcons();

    // If wishlist modal is open, refresh it
    const wishlistModal = document.getElementById('wishlistModal');
    if (wishlistModal.style.display === 'block') {
        // Refresh wishlist modal content
        setTimeout(() => {
            const wishlistItemsContainer = document.getElementById('wishlistItems');
            wishlistItemsContainer.innerHTML = '';

            if (wishlistItems.length === 0) {
                wishlistItemsContainer.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <i class="fas fa-heart" style="font-size: 48px; margin-bottom: 20px; opacity: 0.5;"></i>
                        <h3>قائمة المفضلة فارغة</h3>
                        <p>أضف بعض المنتجات إلى قائمة المفضلة</p>
                    </div>
                `;
            } else {
                wishlistItems.forEach(id => {
                    const prod = products.find(p => p.id === id);
                    if (prod) {
                        const wishlistItem = document.createElement('div');
                        wishlistItem.className = 'wishlist-item';
                        wishlistItem.id = `wishlist-item-${prod.id}`;
                        wishlistItem.style.cssText = `
                            display: flex;
                            align-items: center;
                            gap: 15px;
                            padding: 15px;
                            border: 1px solid #e9ecef;
                            border-radius: 10px;
                            margin-bottom: 15px;
                            transition: all 0.3s ease;
                        `;

                        wishlistItem.innerHTML = `
                            <img src="${prod.image}" alt="${prod.title}" style="width: 80px; height: 60px; object-fit: cover; border-radius: 5px;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div style="display:none; width:80px; height:60px; background:linear-gradient(45deg, #667eea, #764ba2); align-items:center; justify-content:center; color:white; font-size:12px; border-radius:5px; text-align:center;">
                                ${prod.title.substring(0, 10)}...
                            </div>
                            <div style="flex: 1;">
                                <h4 style="margin-bottom: 5px; color: #333;">${prod.title}</h4>
                                <p style="color: #666; font-size: 14px;">${prod.price}</p>
                            </div>
                            <div style="display: flex; gap: 10px;">
                                <button onclick="addToCartFromWishlist(${prod.id})" style="background: #7c4dff; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer; transition: background 0.3s ease;" onmouseover="this.style.background='#6a3de8'" onmouseout="this.style.background='#7c4dff'">
                                    <i class="fas fa-shopping-cart"></i>
                                </button>
                                <button onclick="removeFromWishlist(${prod.id})" style="background: #ff4757; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer; transition: background 0.3s ease;" onmouseover="this.style.background='#ff3742'" onmouseout="this.style.background='#ff4757'">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        `;

                        wishlistItemsContainer.appendChild(wishlistItem);
                    }
                });
            }
        }, 100);
    }
}

// Update wishlist count
function updateWishlistCount() {
    document.getElementById('wishlistCount').textContent = wishlistItems.length;
}

// Update wishlist icons
function updateWishlistIcons() {
    document.querySelectorAll('.product-card').forEach(card => {
        const heartIcon = card.querySelector('.btn-secondary i');
        const productId = parseInt(card.querySelector('.btn-secondary').onclick.toString().match(/\d+/)[0]);
        
        if (wishlistItems.includes(productId)) {
            heartIcon.className = 'fas fa-heart';
        } else {
            heartIcon.className = 'far fa-heart';
        }
    });
}

// Toggle wishlist modal
function toggleWishlist() {
    const modal = document.getElementById('wishlistModal');
    const wishlistItemsContainer = document.getElementById('wishlistItems');

    // Clear previous content
    wishlistItemsContainer.innerHTML = '';

    if (wishlistItems.length === 0) {
        wishlistItemsContainer.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #666;">
                <i class="fas fa-heart" style="font-size: 48px; margin-bottom: 20px; opacity: 0.5;"></i>
                <h3>قائمة المفضلة فارغة</h3>
                <p>أضف بعض المنتجات إلى قائمة المفضلة</p>
            </div>
        `;
    } else {
        wishlistItems.forEach(productId => {
            const product = products.find(p => p.id === productId);
            if (product) {
                const wishlistItem = document.createElement('div');
                wishlistItem.className = 'wishlist-item';
                wishlistItem.id = `wishlist-item-${product.id}`;
                wishlistItem.style.cssText = `
                    display: flex;
                    align-items: center;
                    gap: 15px;
                    padding: 15px;
                    border: 1px solid #e9ecef;
                    border-radius: 10px;
                    margin-bottom: 15px;
                    transition: all 0.3s ease;
                `;

                wishlistItem.innerHTML = `
                    <img src="${product.image}" alt="${product.title}" style="width: 80px; height: 60px; object-fit: cover; border-radius: 5px;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div style="display:none; width:80px; height:60px; background:linear-gradient(45deg, #667eea, #764ba2); align-items:center; justify-content:center; color:white; font-size:12px; border-radius:5px; text-align:center;">
                        ${product.title.substring(0, 10)}...
                    </div>
                    <div style="flex: 1;">
                        <h4 style="margin-bottom: 5px; color: #333;">${product.title}</h4>
                        <p style="color: #666; font-size: 14px;">${product.price}</p>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <button onclick="addToCartFromWishlist(${product.id})" style="background: #7c4dff; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer; transition: background 0.3s ease;" onmouseover="this.style.background='#6a3de8'" onmouseout="this.style.background='#7c4dff'">
                            <i class="fas fa-shopping-cart"></i>
                        </button>
                        <button onclick="removeFromWishlist(${product.id})" style="background: #ff4757; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer; transition: background 0.3s ease;" onmouseover="this.style.background='#ff3742'" onmouseout="this.style.background='#ff4757'">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                `;

                wishlistItemsContainer.appendChild(wishlistItem);
            }
        });
    }

    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';
}

// Close wishlist modal
function closeWishlistModal() {
    document.getElementById('wishlistModal').style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Submit contact form
function submitContactForm(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const name = event.target.querySelector('input[type="text"]').value;
    const email = event.target.querySelector('input[type="email"]').value;
    const message = event.target.querySelector('textarea').value;
    
    // Simulate form submission
    showNotification('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.', 'success');
    
    // Reset form
    event.target.reset();
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    
    let backgroundColor;
    switch(type) {
        case 'success':
            backgroundColor = '#4CAF50';
            break;
        case 'error':
            backgroundColor = '#f44336';
            break;
        case 'warning':
            backgroundColor = '#ff9800';
            break;
        case 'info':
        default:
            backgroundColor = '#2196F3';
            break;
    }
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${backgroundColor};
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        z-index: 1001;
        font-weight: 600;
        animation: slideInRight 0.3s ease;
        max-width: 300px;
        word-wrap: break-word;
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(100px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    @keyframes slideOutRight {
        from {
            opacity: 1;
            transform: translateX(0);
        }
        to {
            opacity: 0;
            transform: translateX(100px);
        }
    }
    
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
`;
document.head.appendChild(style);

// Close modals when clicking outside
window.onclick = function(event) {
    const productModal = document.getElementById('productModal');
    const wishlistModal = document.getElementById('wishlistModal');
    
    if (event.target === productModal) {
        closeProductModal();
    }
    
    if (event.target === wishlistModal) {
        closeWishlistModal();
    }
}

// Add to wishlist from modal
function addToWishlist() {
    if (currentProduct) {
        toggleWishlistItem(currentProduct.id);
    }
}

// Remove from wishlist with animation
function removeFromWishlist(productId) {
    const product = products.find(p => p.id === productId);
    const wishlistItem = document.getElementById(`wishlist-item-${productId}`);

    if (wishlistItem && product) {
        // Add removal animation
        wishlistItem.style.transform = 'translateX(-100%)';
        wishlistItem.style.opacity = '0';

        setTimeout(() => {
            // Remove from array
            const index = wishlistItems.indexOf(productId);
            if (index > -1) {
                wishlistItems.splice(index, 1);
                localStorage.setItem('wishlistItems', JSON.stringify(wishlistItems));
                updateWishlistCount();
                updateWishlistIcons();
            }

            // Remove from DOM
            if (wishlistItem.parentNode) {
                wishlistItem.parentNode.removeChild(wishlistItem);
            }

            // Show notification
            showNotification(`تم حذف "${product.title}" من المفضلة`, 'success');

            // Check if wishlist is empty now
            if (wishlistItems.length === 0) {
                const wishlistItemsContainer = document.getElementById('wishlistItems');
                wishlistItemsContainer.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <i class="fas fa-heart" style="font-size: 48px; margin-bottom: 20px; opacity: 0.5;"></i>
                        <h3>قائمة المفضلة فارغة</h3>
                        <p>أضف بعض المنتجات إلى قائمة المفضلة</p>
                    </div>
                `;
            }
        }, 300);
    }
}

// Add to cart from wishlist
function addToCartFromWishlist(productId) {
    const product = products.find(p => p.id === productId);
    if (product) {
        showNotification(`تم إضافة "${product.title}" إلى السلة`, 'success');

        // Optional: Remove from wishlist after adding to cart
        // removeFromWishlist(productId);
    }
}
