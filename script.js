// Global Variables
let attendanceData = JSON.parse(localStorage.getItem('attendanceData')) || [];
let currentEditId = null;
let currentPhotoData = null;
let editPhotoData = null;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    updateCurrentTime();
    setInterval(updateCurrentTime, 1000);
    loadAttendanceData();
    updateStatistics();
    
    // Set today's date as default for date filter
    document.getElementById('dateFilter').value = new Date().toISOString().split('T')[0];
    
    // Setup form submission
    document.getElementById('editForm').addEventListener('submit', handleEditSubmit);
});

// Update current time display
function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleString('ar-EG', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    document.getElementById('currentTime').textContent = timeString;
}

// Text-to-Speech function
function speakName() {
    const employeeName = document.getElementById('employeeName').value.trim();
    
    if (!employeeName) {
        alert('يرجى إدخال اسم الموظف أولاً');
        return;
    }
    
    // Check if browser supports speech synthesis
    if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(employeeName);
        utterance.lang = 'ar-SA'; // Arabic language
        utterance.rate = 0.8;
        utterance.pitch = 1;
        utterance.volume = 1;
        
        // Get available voices
        const voices = speechSynthesis.getVoices();
        const arabicVoice = voices.find(voice => voice.lang.includes('ar'));
        if (arabicVoice) {
            utterance.voice = arabicVoice;
        }
        
        speechSynthesis.speak(utterance);
    } else {
        alert('المتصفح لا يدعم خاصية النطق');
    }
}

// Record attendance (checkin/checkout)
function recordAttendance(type) {
    const employeeName = document.getElementById('employeeName').value.trim();
    const employeeId = document.getElementById('employeeId').value.trim();
    
    if (!employeeName || !employeeId) {
        alert('يرجى إدخال اسم الموظف ورقم الموظف');
        return;
    }
    
    const now = new Date();
    const today = now.toISOString().split('T')[0];
    const currentTime = now.toTimeString().split(' ')[0].substring(0, 5);
    
    // Check if employee already has a record for today
    let existingRecord = attendanceData.find(record => 
        record.employeeId === employeeId && record.date === today
    );
    
    if (type === 'checkin') {
        if (existingRecord && existingRecord.checkinTime) {
            alert('تم تسجيل حضور هذا الموظف مسبقاً اليوم');
            return;
        }
        
        if (existingRecord) {
            existingRecord.checkinTime = currentTime;
            existingRecord.status = 'حضور';
        } else {
            const newRecord = {
                id: Date.now(),
                employeeName: employeeName,
                employeeId: employeeId,
                date: today,
                checkinTime: currentTime,
                checkoutTime: '',
                workHours: '',
                status: 'حضور',
                photo: currentPhotoData || null
            };
            attendanceData.push(newRecord);
        }
        
        // Speak the name on checkin
        setTimeout(() => {
            speakName();
        }, 500);
        
        showNotification('تم تسجيل الحضور بنجاح', 'success');
        
    } else if (type === 'checkout') {
        if (!existingRecord || !existingRecord.checkinTime) {
            alert('يجب تسجيل الحضور أولاً');
            return;
        }
        
        if (existingRecord.checkoutTime) {
            alert('تم تسجيل انصراف هذا الموظف مسبقاً اليوم');
            return;
        }
        
        existingRecord.checkoutTime = currentTime;
        existingRecord.workHours = calculateWorkHours(existingRecord.checkinTime, currentTime);
        existingRecord.status = 'انصراف';
        
        // Speak checkout message
        setTimeout(() => {
            const utterance = new SpeechSynthesisUtterance(`انصراف ${employeeName}`);
            utterance.lang = 'ar-SA';
            utterance.rate = 0.8;
            speechSynthesis.speak(utterance);
        }, 500);
        
        showNotification('تم تسجيل الانصراف بنجاح', 'success');
    }
    
    saveAttendanceData();
    loadAttendanceData();
    updateStatistics();
    clearInputs();
}

// Calculate work hours
function calculateWorkHours(checkinTime, checkoutTime) {
    const checkin = new Date(`2000-01-01 ${checkinTime}`);
    const checkout = new Date(`2000-01-01 ${checkoutTime}`);
    const diffMs = checkout - checkin;
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    return `${diffHours}:${diffMinutes.toString().padStart(2, '0')}`;
}

// Handle photo upload
function handlePhotoUpload(event) {
    const file = event.target.files[0];
    if (file) {
        if (file.size > 5 * 1024 * 1024) { // 5MB limit
            alert('حجم الصورة كبير جداً. يرجى اختيار صورة أصغر من 5 ميجابايت');
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            currentPhotoData = e.target.result;
            const preview = document.getElementById('photoPreview');
            preview.innerHTML = `<img src="${currentPhotoData}" alt="صورة الموظف">`;
            preview.classList.add('has-image');
        };
        reader.readAsDataURL(file);
    }
}

// Handle edit photo upload
function handleEditPhotoUpload(event) {
    const file = event.target.files[0];
    if (file) {
        if (file.size > 5 * 1024 * 1024) { // 5MB limit
            alert('حجم الصورة كبير جداً. يرجى اختيار صورة أصغر من 5 ميجابايت');
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            editPhotoData = e.target.result;
            const preview = document.getElementById('editPhotoPreview');
            preview.innerHTML = `<img src="${editPhotoData}" alt="صورة الموظف">`;
            preview.classList.add('has-image');
        };
        reader.readAsDataURL(file);
    }
}

// Clear input fields
function clearInputs() {
    document.getElementById('employeeName').value = '';
    document.getElementById('employeeId').value = '';
    document.getElementById('employeePhoto').value = '';
    currentPhotoData = null;

    const preview = document.getElementById('photoPreview');
    preview.innerHTML = `
        <i class="fas fa-camera"></i>
        <span>اختر صورة الموظف</span>
    `;
    preview.classList.remove('has-image');
}

// Save data to localStorage
function saveAttendanceData() {
    localStorage.setItem('attendanceData', JSON.stringify(attendanceData));
}

// Load and display attendance data
function loadAttendanceData() {
    const tableBody = document.getElementById('attendanceTableBody');
    tableBody.innerHTML = '';
    
    // Sort data by date and time (newest first)
    const sortedData = [...attendanceData].sort((a, b) => {
        const dateA = new Date(`${a.date} ${a.checkinTime || '00:00'}`);
        const dateB = new Date(`${b.date} ${b.checkinTime || '00:00'}`);
        return dateB - dateA;
    });
    
    sortedData.forEach(record => {
        const row = createTableRow(record);
        tableBody.appendChild(row);
    });
}

// Create table row
function createTableRow(record) {
    const row = document.createElement('tr');
    row.className = 'fade-in';

    const statusClass = record.status === 'حضور' ? 'status-present' :
                       record.status === 'انصراف' ? 'status-present' : 'status-absent';

    // Create photo cell
    let photoCell = '';
    if (record.photo) {
        photoCell = `<img src="${record.photo}" alt="صورة ${record.employeeName}" class="employee-photo" onclick="showPhotoModal('${record.photo}', '${record.employeeName}')">`;
    } else {
        photoCell = `<div class="no-photo"><i class="fas fa-user"></i></div>`;
    }

    row.innerHTML = `
        <td>${photoCell}</td>
        <td>${record.employeeId}</td>
        <td>${record.employeeName}</td>
        <td>${new Date(record.date).toLocaleDateString('ar-EG')}</td>
        <td>${record.checkinTime || '-'}</td>
        <td>${record.checkoutTime || '-'}</td>
        <td>${record.workHours || '-'}</td>
        <td><span class="${statusClass}">${record.status}</span></td>
        <td>
            <button class="action-btn btn-edit" onclick="editRecord(${record.id})">
                <i class="fas fa-edit"></i> تعديل
            </button>
            <button class="action-btn btn-delete" onclick="deleteRecord(${record.id})">
                <i class="fas fa-trash"></i> حذف
            </button>
        </td>
    `;

    return row;
}

// Filter table based on search criteria
function filterTable() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const dateFilter = document.getElementById('dateFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    
    const tableBody = document.getElementById('attendanceTableBody');
    const rows = tableBody.getElementsByTagName('tr');
    
    Array.from(rows).forEach(row => {
        const employeeName = row.cells[1].textContent.toLowerCase();
        const employeeId = row.cells[0].textContent.toLowerCase();
        const recordDate = row.cells[2].textContent;
        const status = row.cells[6].textContent.trim();
        
        const matchesSearch = employeeName.includes(searchTerm) || employeeId.includes(searchTerm);
        const matchesDate = !dateFilter || recordDate.includes(new Date(dateFilter).toLocaleDateString('ar-EG'));
        const matchesStatus = !statusFilter || status.includes(statusFilter);
        
        if (matchesSearch && matchesDate && matchesStatus) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// Update statistics
function updateStatistics() {
    const today = new Date().toISOString().split('T')[0];
    const uniqueEmployees = [...new Set(attendanceData.map(record => record.employeeId))];
    const presentToday = attendanceData.filter(record => 
        record.date === today && record.checkinTime
    ).length;
    
    // Calculate average work hours
    const recordsWithWorkHours = attendanceData.filter(record => record.workHours);
    let totalMinutes = 0;
    recordsWithWorkHours.forEach(record => {
        const [hours, minutes] = record.workHours.split(':').map(Number);
        totalMinutes += hours * 60 + minutes;
    });
    const avgWorkHours = recordsWithWorkHours.length > 0 ? 
        (totalMinutes / recordsWithWorkHours.length / 60).toFixed(1) : 0;
    
    document.getElementById('totalEmployees').textContent = uniqueEmployees.length;
    document.getElementById('presentToday').textContent = presentToday;
    document.getElementById('avgWorkHours').textContent = avgWorkHours;
}

// Show photo modal
function showPhotoModal(photoSrc, employeeName) {
    document.getElementById('photoModalTitle').textContent = `صورة ${employeeName}`;
    document.getElementById('photoModalImage').src = photoSrc;
    document.getElementById('photoModal').style.display = 'block';
}

// Close photo modal
function closePhotoModal() {
    document.getElementById('photoModal').style.display = 'none';
}

// Edit record
function editRecord(id) {
    const record = attendanceData.find(r => r.id === id);
    if (!record) return;

    currentEditId = id;
    editPhotoData = record.photo || null;

    document.getElementById('editRecordId').value = id;
    document.getElementById('editEmployeeName').value = record.employeeName;
    document.getElementById('editEmployeeId').value = record.employeeId;
    document.getElementById('editDate').value = record.date;
    document.getElementById('editCheckinTime').value = record.checkinTime || '';
    document.getElementById('editCheckoutTime').value = record.checkoutTime || '';

    // Set photo preview
    const preview = document.getElementById('editPhotoPreview');
    if (record.photo) {
        preview.innerHTML = `<img src="${record.photo}" alt="صورة الموظف">`;
        preview.classList.add('has-image');
    } else {
        preview.innerHTML = `
            <i class="fas fa-camera"></i>
            <span>اختر صورة الموظف</span>
        `;
        preview.classList.remove('has-image');
    }

    document.getElementById('editModal').style.display = 'block';
}

// Handle edit form submission
function handleEditSubmit(e) {
    e.preventDefault();
    
    const id = parseInt(document.getElementById('editRecordId').value);
    const record = attendanceData.find(r => r.id === id);
    
    if (!record) return;
    
    record.employeeName = document.getElementById('editEmployeeName').value;
    record.employeeId = document.getElementById('editEmployeeId').value;
    record.date = document.getElementById('editDate').value;
    record.checkinTime = document.getElementById('editCheckinTime').value;
    record.checkoutTime = document.getElementById('editCheckoutTime').value;
    record.photo = editPhotoData;
    
    // Recalculate work hours if both times are present
    if (record.checkinTime && record.checkoutTime) {
        record.workHours = calculateWorkHours(record.checkinTime, record.checkoutTime);
        record.status = 'انصراف';
    } else if (record.checkinTime) {
        record.status = 'حضور';
        record.workHours = '';
    }
    
    saveAttendanceData();
    loadAttendanceData();
    updateStatistics();
    closeEditModal();
    
    showNotification('تم تحديث السجل بنجاح', 'success');
}

// Close edit modal
function closeEditModal() {
    document.getElementById('editModal').style.display = 'none';
    currentEditId = null;
    editPhotoData = null;

    // Reset photo preview
    const preview = document.getElementById('editPhotoPreview');
    preview.innerHTML = `
        <i class="fas fa-camera"></i>
        <span>اختر صورة الموظف</span>
    `;
    preview.classList.remove('has-image');
}

// Delete record
function deleteRecord(id) {
    if (confirm('هل أنت متأكد من حذف هذا السجل؟')) {
        attendanceData = attendanceData.filter(record => record.id !== id);
        saveAttendanceData();
        loadAttendanceData();
        updateStatistics();
        showNotification('تم حذف السجل بنجاح', 'success');
    }
}

// Export to Excel
function exportToExcel() {
    if (attendanceData.length === 0) {
        alert('لا توجد بيانات للتصدير');
        return;
    }
    
    // Create CSV content
    let csvContent = '\uFEFF'; // BOM for UTF-8
    csvContent += 'رقم الموظف,اسم الموظف,التاريخ,وقت الحضور,وقت الانصراف,ساعات العمل,الحالة,يوجد صورة\n';

    attendanceData.forEach(record => {
        const hasPhoto = record.photo ? 'نعم' : 'لا';
        csvContent += `${record.employeeId},${record.employeeName},${record.date},${record.checkinTime || ''},${record.checkoutTime || ''},${record.workHours || ''},${record.status},${hasPhoto}\n`;
    });
    
    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `attendance_report_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    showNotification('تم تصدير البيانات بنجاح', 'success');
}

// Show notification
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        z-index: 1001;
        font-weight: 600;
        animation: slideInRight 0.3s ease;
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Add CSS animations for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(100px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    @keyframes slideOutRight {
        from {
            opacity: 1;
            transform: translateX(0);
        }
        to {
            opacity: 0;
            transform: translateX(100px);
        }
    }
`;
document.head.appendChild(style);

// Close modal when clicking outside
window.onclick = function(event) {
    const editModal = document.getElementById('editModal');
    const photoModal = document.getElementById('photoModal');

    if (event.target === editModal) {
        closeEditModal();
    }

    if (event.target === photoModal) {
        closePhotoModal();
    }
}

// Load voices when they become available
if ('speechSynthesis' in window) {
    speechSynthesis.onvoiceschanged = function() {
        // Voices are now loaded
    };
}
