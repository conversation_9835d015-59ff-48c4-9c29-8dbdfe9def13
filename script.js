// Global Variables
let attendanceData = JSON.parse(localStorage.getItem('attendanceData')) || [];
let currentEditId = null;
let currentPhotoData = null;
let editPhotoData = null;

// Work schedule constants
const WORK_START_TIME = '08:00';
const WORK_END_TIME = '20:00';
const WORK_HOURS_PER_DAY = 12;
let autoCheckoutInterval;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    updateCurrentTime();
    setInterval(updateCurrentTime, 1000);
    loadAttendanceData();
    updateStatistics();
    populateMonthSelector();
    updateMonthlyStats();

    // Set today's date as default for date filter
    document.getElementById('dateFilter').value = new Date().toISOString().split('T')[0];

    // Setup form submission
    document.getElementById('editForm').addEventListener('submit', handleEditSubmit);

    // Start auto-checkout monitoring
    startAutoCheckoutMonitoring();
});

// Update current time display
function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleString('ar-EG', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    document.getElementById('currentTime').textContent = timeString;
}

// Text-to-Speech function
function speakName() {
    const employeeName = document.getElementById('employeeName').value.trim();
    
    if (!employeeName) {
        alert('يرجى إدخال اسم الموظف أولاً');
        return;
    }
    
    // Check if browser supports speech synthesis
    if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(employeeName);
        utterance.lang = 'ar-SA'; // Arabic language
        utterance.rate = 0.8;
        utterance.pitch = 1;
        utterance.volume = 1;
        
        // Get available voices
        const voices = speechSynthesis.getVoices();
        const arabicVoice = voices.find(voice => voice.lang.includes('ar'));
        if (arabicVoice) {
            utterance.voice = arabicVoice;
        }
        
        speechSynthesis.speak(utterance);
    } else {
        alert('المتصفح لا يدعم خاصية النطق');
    }
}

// Start auto-checkout monitoring
function startAutoCheckoutMonitoring() {
    // Check every minute for auto-checkout
    autoCheckoutInterval = setInterval(() => {
        checkAutoCheckout();
    }, 60000); // Check every minute
}

// Check for automatic checkout at 8 PM
function checkAutoCheckout() {
    const now = new Date();
    const currentTime = now.toTimeString().split(' ')[0].substring(0, 5);
    const today = now.toISOString().split('T')[0];

    // Check if it's exactly 8 PM (20:00)
    if (currentTime === WORK_END_TIME) {
        // Find all employees who are still checked in
        const employeesToCheckout = attendanceData.filter(record =>
            record.date === today &&
            record.checkinTime &&
            !record.checkoutTime
        );

        employeesToCheckout.forEach(record => {
            record.checkoutTime = WORK_END_TIME;
            record.workHours = calculateWorkHours(record.checkinTime, WORK_END_TIME);
            record.status = 'انصراف';
            record.checkoutType = 'تلقائي';

            // Speak auto checkout message
            setTimeout(() => {
                const utterance = new SpeechSynthesisUtterance(`انصراف تلقائي ${record.employeeName}`);
                utterance.lang = 'ar-SA';
                utterance.rate = 0.8;
                speechSynthesis.speak(utterance);
            }, 1000);
        });

        if (employeesToCheckout.length > 0) {
            saveAttendanceData();
            loadAttendanceData();
            updateStatistics();
            showNotification(`تم تسجيل انصراف تلقائي لـ ${employeesToCheckout.length} موظف`, 'info');
        }
    }
}

// Record attendance (checkin/checkout)
function recordAttendance(type) {
    const employeeName = document.getElementById('employeeName').value.trim();
    const employeeId = document.getElementById('employeeId').value.trim();

    if (!employeeName || !employeeId) {
        alert('يرجى إدخال اسم الموظف ورقم الموظف');
        return;
    }

    const now = new Date();
    const today = now.toISOString().split('T')[0];
    const currentTime = now.toTimeString().split(' ')[0].substring(0, 5);

    // Check if employee already has a record for today
    let existingRecord = attendanceData.find(record =>
        record.employeeId === employeeId && record.date === today
    );

    if (type === 'checkin') {
        // Check if it's before work hours
        if (currentTime < WORK_START_TIME) {
            if (!confirm(`الوقت الحالي ${currentTime} قبل بداية العمل الرسمية (${WORK_START_TIME}). هل تريد المتابعة؟`)) {
                return;
            }
        }

        if (existingRecord && existingRecord.checkinTime) {
            alert('تم تسجيل حضور هذا الموظف مسبقاً اليوم');
            return;
        }

        if (existingRecord) {
            existingRecord.checkinTime = currentTime;
            existingRecord.status = 'حضور';
            existingRecord.checkoutType = '';
        } else {
            const newRecord = {
                id: Date.now(),
                employeeName: employeeName,
                employeeId: employeeId,
                date: today,
                checkinTime: currentTime,
                checkoutTime: '',
                workHours: '',
                status: 'حضور',
                checkoutType: '',
                photo: currentPhotoData || null
            };
            attendanceData.push(newRecord);
        }

        // Speak the name on checkin
        setTimeout(() => {
            speakName();
        }, 500);

        showNotification('تم تسجيل الحضور بنجاح', 'success');

    } else if (type === 'checkout') {
        if (!existingRecord || !existingRecord.checkinTime) {
            alert('يجب تسجيل الحضور أولاً');
            return;
        }

        if (existingRecord.checkoutTime) {
            alert('تم تسجيل انصراف هذا الموظف مسبقاً اليوم');
            return;
        }

        existingRecord.checkoutTime = currentTime;
        existingRecord.workHours = calculateWorkHours(existingRecord.checkinTime, currentTime);
        existingRecord.status = 'انصراف';
        existingRecord.checkoutType = 'يدوي';

        // Check if leaving early
        if (currentTime < WORK_END_TIME) {
            const workHoursNum = parseFloat(existingRecord.workHours.replace(':', '.'));
            if (workHoursNum < WORK_HOURS_PER_DAY) {
                showNotification(`انصراف مبكر - ساعات العمل: ${existingRecord.workHours}`, 'warning');
            }
        }

        // Speak checkout message
        setTimeout(() => {
            const utterance = new SpeechSynthesisUtterance(`انصراف ${employeeName}`);
            utterance.lang = 'ar-SA';
            utterance.rate = 0.8;
            speechSynthesis.speak(utterance);
        }, 500);

        showNotification('تم تسجيل الانصراف بنجاح', 'success');
    }

    saveAttendanceData();
    loadAttendanceData();
    updateStatistics();
    updateMonthlyStats();
    clearInputs();
}

// Calculate work hours
function calculateWorkHours(checkinTime, checkoutTime) {
    const checkin = new Date(`2000-01-01 ${checkinTime}`);
    const checkout = new Date(`2000-01-01 ${checkoutTime}`);

    // Handle next day checkout (if checkout is before checkin, assume next day)
    if (checkout < checkin) {
        checkout.setDate(checkout.getDate() + 1);
    }

    const diffMs = checkout - checkin;
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    return `${diffHours}:${diffMinutes.toString().padStart(2, '0')}`;
}

// Convert time string to decimal hours
function timeToDecimal(timeStr) {
    if (!timeStr) return 0;
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours + (minutes / 60);
}

// Handle photo upload
function handlePhotoUpload(event) {
    const file = event.target.files[0];
    if (file) {
        if (file.size > 5 * 1024 * 1024) { // 5MB limit
            alert('حجم الصورة كبير جداً. يرجى اختيار صورة أصغر من 5 ميجابايت');
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            currentPhotoData = e.target.result;
            const preview = document.getElementById('photoPreview');
            preview.innerHTML = `<img src="${currentPhotoData}" alt="صورة الموظف">`;
            preview.classList.add('has-image');
        };
        reader.readAsDataURL(file);
    }
}

// Handle edit photo upload
function handleEditPhotoUpload(event) {
    const file = event.target.files[0];
    if (file) {
        if (file.size > 5 * 1024 * 1024) { // 5MB limit
            alert('حجم الصورة كبير جداً. يرجى اختيار صورة أصغر من 5 ميجابايت');
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            editPhotoData = e.target.result;
            const preview = document.getElementById('editPhotoPreview');
            preview.innerHTML = `<img src="${editPhotoData}" alt="صورة الموظف">`;
            preview.classList.add('has-image');
        };
        reader.readAsDataURL(file);
    }
}

// Clear input fields
function clearInputs() {
    document.getElementById('employeeName').value = '';
    document.getElementById('employeeId').value = '';
    document.getElementById('employeePhoto').value = '';
    currentPhotoData = null;

    const preview = document.getElementById('photoPreview');
    preview.innerHTML = `
        <i class="fas fa-camera"></i>
        <span>اختر صورة الموظف</span>
    `;
    preview.classList.remove('has-image');
}

// Save data to localStorage
function saveAttendanceData() {
    localStorage.setItem('attendanceData', JSON.stringify(attendanceData));
}

// Load and display attendance data
function loadAttendanceData() {
    const tableBody = document.getElementById('attendanceTableBody');
    tableBody.innerHTML = '';
    
    // Sort data by date and time (newest first)
    const sortedData = [...attendanceData].sort((a, b) => {
        const dateA = new Date(`${a.date} ${a.checkinTime || '00:00'}`);
        const dateB = new Date(`${b.date} ${b.checkinTime || '00:00'}`);
        return dateB - dateA;
    });
    
    sortedData.forEach(record => {
        const row = createTableRow(record);
        tableBody.appendChild(row);
    });
}

// Create table row
function createTableRow(record) {
    const row = document.createElement('tr');
    row.className = 'fade-in';

    const statusClass = record.status === 'حضور' ? 'status-present' :
                       record.status === 'انصراف' ? 'status-present' : 'status-absent';

    // Create photo cell
    let photoCell = '';
    if (record.photo) {
        photoCell = `<img src="${record.photo}" alt="صورة ${record.employeeName}" class="employee-photo" onclick="showPhotoModal('${record.photo}', '${record.employeeName}')">`;
    } else {
        photoCell = `<div class="no-photo"><i class="fas fa-user"></i></div>`;
    }

    // Create checkout type cell
    let checkoutTypeCell = '-';
    if (record.checkoutType) {
        const checkoutClass = record.checkoutType === 'تلقائي' ? 'checkout-auto' : 'checkout-manual';
        checkoutTypeCell = `<span class="${checkoutClass}">${record.checkoutType}</span>`;
    }

    // Check for overtime
    let workHoursDisplay = record.workHours || '-';
    if (record.workHours) {
        const workHoursDecimal = timeToDecimal(record.workHours);
        if (workHoursDecimal > WORK_HOURS_PER_DAY) {
            workHoursDisplay += ` <span class="overtime-indicator">إضافي</span>`;
        }
    }

    row.innerHTML = `
        <td>${photoCell}</td>
        <td>${record.employeeId}</td>
        <td>${record.employeeName}</td>
        <td>${new Date(record.date).toLocaleDateString('ar-EG')}</td>
        <td>${record.checkinTime || '-'}</td>
        <td>${record.checkoutTime || '-'}</td>
        <td>${workHoursDisplay}</td>
        <td><span class="${statusClass}">${record.status}</span></td>
        <td>${checkoutTypeCell}</td>
        <td>
            <button class="action-btn btn-edit" onclick="editRecord(${record.id})">
                <i class="fas fa-edit"></i> تعديل
            </button>
            <button class="action-btn btn-delete" onclick="deleteRecord(${record.id})">
                <i class="fas fa-trash"></i> حذف
            </button>
        </td>
    `;

    return row;
}

// Filter table based on search criteria
function filterTable() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const dateFilter = document.getElementById('dateFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    
    const tableBody = document.getElementById('attendanceTableBody');
    const rows = tableBody.getElementsByTagName('tr');
    
    Array.from(rows).forEach(row => {
        const employeeName = row.cells[1].textContent.toLowerCase();
        const employeeId = row.cells[0].textContent.toLowerCase();
        const recordDate = row.cells[2].textContent;
        const status = row.cells[6].textContent.trim();
        
        const matchesSearch = employeeName.includes(searchTerm) || employeeId.includes(searchTerm);
        const matchesDate = !dateFilter || recordDate.includes(new Date(dateFilter).toLocaleDateString('ar-EG'));
        const matchesStatus = !statusFilter || status.includes(statusFilter);
        
        if (matchesSearch && matchesDate && matchesStatus) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// Update statistics
function updateStatistics() {
    const today = new Date().toISOString().split('T')[0];
    const currentMonth = new Date().toISOString().substring(0, 7); // YYYY-MM

    const uniqueEmployees = [...new Set(attendanceData.map(record => record.employeeId))];
    const presentToday = attendanceData.filter(record =>
        record.date === today && record.checkinTime
    ).length;

    // Calculate today's total work hours
    const todayRecords = attendanceData.filter(record =>
        record.date === today && record.workHours
    );
    let todayTotalMinutes = 0;
    todayRecords.forEach(record => {
        const [hours, minutes] = record.workHours.split(':').map(Number);
        todayTotalMinutes += hours * 60 + minutes;
    });
    const todayWorkHours = (todayTotalMinutes / 60).toFixed(1);

    // Calculate this month's work days
    const thisMonthRecords = attendanceData.filter(record =>
        record.date.startsWith(currentMonth) && record.checkinTime
    );
    const workDaysThisMonth = [...new Set(thisMonthRecords.map(record => record.date))].length;

    // Calculate this month's total work hours
    const monthlyRecordsWithHours = attendanceData.filter(record =>
        record.date.startsWith(currentMonth) && record.workHours
    );
    let monthlyTotalMinutes = 0;
    monthlyRecordsWithHours.forEach(record => {
        const [hours, minutes] = record.workHours.split(':').map(Number);
        monthlyTotalMinutes += hours * 60 + minutes;
    });
    const monthlyWorkHours = (monthlyTotalMinutes / 60).toFixed(1);

    document.getElementById('totalEmployees').textContent = uniqueEmployees.length;
    document.getElementById('presentToday').textContent = presentToday;
    document.getElementById('todayWorkHours').textContent = todayWorkHours;
    document.getElementById('workDaysThisMonth').textContent = workDaysThisMonth;
    document.getElementById('monthlyWorkHours').textContent = monthlyWorkHours;
}

// Populate month selector
function populateMonthSelector() {
    const monthSelector = document.getElementById('monthSelector');
    const months = [...new Set(attendanceData.map(record => record.date.substring(0, 7)))];

    // Add current month if not in data
    const currentMonth = new Date().toISOString().substring(0, 7);
    if (!months.includes(currentMonth)) {
        months.push(currentMonth);
    }

    months.sort().reverse(); // Most recent first

    monthSelector.innerHTML = '';
    months.forEach(month => {
        const option = document.createElement('option');
        option.value = month;
        const [year, monthNum] = month.split('-');
        const monthNames = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                           'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
        option.textContent = `${monthNames[parseInt(monthNum) - 1]} ${year}`;
        if (month === currentMonth) {
            option.selected = true;
        }
        monthSelector.appendChild(option);
    });
}

// Update monthly statistics
function updateMonthlyStats() {
    const selectedMonth = document.getElementById('monthSelector').value;
    if (!selectedMonth) return;

    const monthlyRecords = attendanceData.filter(record =>
        record.date.startsWith(selectedMonth)
    );

    // Calculate work days
    const workDays = [...new Set(monthlyRecords.filter(record => record.checkinTime).map(record => record.date))].length;

    // Calculate total work hours
    const recordsWithHours = monthlyRecords.filter(record => record.workHours);
    let totalMinutes = 0;
    recordsWithHours.forEach(record => {
        const [hours, minutes] = record.workHours.split(':').map(Number);
        totalMinutes += hours * 60 + minutes;
    });
    const totalHours = (totalMinutes / 60).toFixed(1);

    // Calculate average hours per day
    const avgHours = workDays > 0 ? (totalMinutes / 60 / workDays).toFixed(1) : 0;

    // Calculate full attendance days (12 hours or more)
    const fullAttendanceDays = recordsWithHours.filter(record => {
        const workHoursDecimal = timeToDecimal(record.workHours);
        return workHoursDecimal >= WORK_HOURS_PER_DAY;
    }).length;

    document.getElementById('monthlyWorkDays').textContent = workDays;
    document.getElementById('monthlyTotalHours').textContent = totalHours;
    document.getElementById('monthlyAvgHours').textContent = avgHours;
    document.getElementById('fullAttendanceDays').textContent = fullAttendanceDays;
}

// Show photo modal
function showPhotoModal(photoSrc, employeeName) {
    document.getElementById('photoModalTitle').textContent = `صورة ${employeeName}`;
    document.getElementById('photoModalImage').src = photoSrc;
    document.getElementById('photoModal').style.display = 'block';
}

// Close photo modal
function closePhotoModal() {
    document.getElementById('photoModal').style.display = 'none';
}

// Edit record
function editRecord(id) {
    const record = attendanceData.find(r => r.id === id);
    if (!record) return;

    currentEditId = id;
    editPhotoData = record.photo || null;

    document.getElementById('editRecordId').value = id;
    document.getElementById('editEmployeeName').value = record.employeeName;
    document.getElementById('editEmployeeId').value = record.employeeId;
    document.getElementById('editDate').value = record.date;
    document.getElementById('editCheckinTime').value = record.checkinTime || '';
    document.getElementById('editCheckoutTime').value = record.checkoutTime || '';

    // Set photo preview
    const preview = document.getElementById('editPhotoPreview');
    if (record.photo) {
        preview.innerHTML = `<img src="${record.photo}" alt="صورة الموظف">`;
        preview.classList.add('has-image');
    } else {
        preview.innerHTML = `
            <i class="fas fa-camera"></i>
            <span>اختر صورة الموظف</span>
        `;
        preview.classList.remove('has-image');
    }

    document.getElementById('editModal').style.display = 'block';
}

// Handle edit form submission
function handleEditSubmit(e) {
    e.preventDefault();
    
    const id = parseInt(document.getElementById('editRecordId').value);
    const record = attendanceData.find(r => r.id === id);
    
    if (!record) return;
    
    record.employeeName = document.getElementById('editEmployeeName').value;
    record.employeeId = document.getElementById('editEmployeeId').value;
    record.date = document.getElementById('editDate').value;
    record.checkinTime = document.getElementById('editCheckinTime').value;
    record.checkoutTime = document.getElementById('editCheckoutTime').value;
    record.photo = editPhotoData;

    // Recalculate work hours if both times are present
    if (record.checkinTime && record.checkoutTime) {
        record.workHours = calculateWorkHours(record.checkinTime, record.checkoutTime);
        record.status = 'انصراف';
        // Keep existing checkout type or set to manual if not set
        if (!record.checkoutType) {
            record.checkoutType = 'يدوي';
        }
    } else if (record.checkinTime) {
        record.status = 'حضور';
        record.workHours = '';
        record.checkoutType = '';
    }
    
    saveAttendanceData();
    loadAttendanceData();
    updateStatistics();
    updateMonthlyStats();
    populateMonthSelector();
    closeEditModal();

    showNotification('تم تحديث السجل بنجاح', 'success');
}

// Close edit modal
function closeEditModal() {
    document.getElementById('editModal').style.display = 'none';
    currentEditId = null;
    editPhotoData = null;

    // Reset photo preview
    const preview = document.getElementById('editPhotoPreview');
    preview.innerHTML = `
        <i class="fas fa-camera"></i>
        <span>اختر صورة الموظف</span>
    `;
    preview.classList.remove('has-image');
}

// Delete record
function deleteRecord(id) {
    if (confirm('هل أنت متأكد من حذف هذا السجل؟')) {
        attendanceData = attendanceData.filter(record => record.id !== id);
        saveAttendanceData();
        loadAttendanceData();
        updateStatistics();
        updateMonthlyStats();
        populateMonthSelector();
        showNotification('تم حذف السجل بنجاح', 'success');
    }
}

// Export to Excel
function exportToExcel() {
    if (attendanceData.length === 0) {
        alert('لا توجد بيانات للتصدير');
        return;
    }
    
    // Create CSV content
    let csvContent = '\uFEFF'; // BOM for UTF-8
    csvContent += 'رقم الموظف,اسم الموظف,التاريخ,وقت الحضور,وقت الانصراف,ساعات العمل,الحالة,نوع الانصراف,يوجد صورة,ساعات إضافية\n';

    attendanceData.forEach(record => {
        const hasPhoto = record.photo ? 'نعم' : 'لا';
        const checkoutType = record.checkoutType || '-';
        const workHoursDecimal = record.workHours ? timeToDecimal(record.workHours) : 0;
        const isOvertime = workHoursDecimal > WORK_HOURS_PER_DAY ? 'نعم' : 'لا';

        csvContent += `${record.employeeId},${record.employeeName},${record.date},${record.checkinTime || ''},${record.checkoutTime || ''},${record.workHours || ''},${record.status},${checkoutType},${hasPhoto},${isOvertime}\n`;
    });
    
    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `attendance_report_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    showNotification('تم تصدير البيانات بنجاح', 'success');
}

// Show notification
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;

    let backgroundColor;
    switch(type) {
        case 'success':
            backgroundColor = '#4CAF50';
            break;
        case 'error':
            backgroundColor = '#f44336';
            break;
        case 'warning':
            backgroundColor = '#ff9800';
            break;
        case 'info':
        default:
            backgroundColor = '#2196F3';
            break;
    }

    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${backgroundColor};
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        z-index: 1001;
        font-weight: 600;
        animation: slideInRight 0.3s ease;
        max-width: 300px;
        word-wrap: break-word;
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Remove notification after 4 seconds for warnings, 3 for others
    const duration = type === 'warning' ? 4000 : 3000;
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, duration);
}

// Add CSS animations for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(100px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    @keyframes slideOutRight {
        from {
            opacity: 1;
            transform: translateX(0);
        }
        to {
            opacity: 0;
            transform: translateX(100px);
        }
    }
`;
document.head.appendChild(style);

// Close modal when clicking outside
window.onclick = function(event) {
    const editModal = document.getElementById('editModal');
    const photoModal = document.getElementById('photoModal');

    if (event.target === editModal) {
        closeEditModal();
    }

    if (event.target === photoModal) {
        closePhotoModal();
    }
}

// Load voices when they become available
if ('speechSynthesis' in window) {
    speechSynthesis.onvoiceschanged = function() {
        // Voices are now loaded
    };
}
