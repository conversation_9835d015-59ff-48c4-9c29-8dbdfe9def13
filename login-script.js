// Global Variables
let users = JSON.parse(localStorage.getItem('egbank_users')) || {
    'admin': {
        password: 'admin123',
        email: '<EMAIL>',
        fullName: 'مدير النظام',
        role: 'admin',
        createdAt: new Date().toISOString()
    },
    'user': {
        password: 'user123',
        email: '<EMAIL>',
        fullName: 'مستخدم عادي',
        role: 'user',
        createdAt: new Date().toISOString()
    },
    'guest': {
        password: 'guest123',
        email: '<EMAIL>',
        fullName: 'مستخدم ضيف',
        role: 'guest',
        createdAt: new Date().toISOString()
    }
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is already logged in
    checkExistingLogin();
    
    // Setup form event listeners
    setupEventListeners();
    
    // Load saved username if remember me was checked
    loadSavedCredentials();
});

// Check if user is already logged in
function checkExistingLogin() {
    const currentUser = localStorage.getItem('egbank_current_user');
    const loginTime = localStorage.getItem('egbank_login_time');
    
    if (currentUser && loginTime) {
        const loginDate = new Date(loginTime);
        const now = new Date();
        const hoursDiff = (now - loginDate) / (1000 * 60 * 60);
        
        // Auto logout after 24 hours
        if (hoursDiff < 24) {
            redirectToStore();
        } else {
            logout();
        }
    }
}

// Setup event listeners
function setupEventListeners() {
    // Login form
    document.getElementById('loginForm').addEventListener('submit', handleLogin);
    
    // Register form
    document.getElementById('registerForm').addEventListener('submit', handleRegister);
    
    // Forgot password form
    document.getElementById('forgotPasswordForm').addEventListener('submit', handleForgotPassword);
    
    // Close modals when clicking outside
    window.onclick = function(event) {
        const registerModal = document.getElementById('registerModal');
        const forgotModal = document.getElementById('forgotPasswordModal');
        
        if (event.target === registerModal) {
            closeRegisterModal();
        }
        if (event.target === forgotModal) {
            closeForgotPasswordModal();
        }
    }
}

// Handle login form submission
function handleLogin(event) {
    event.preventDefault();
    
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;
    const rememberMe = document.getElementById('rememberMe').checked;
    
    if (!username || !password) {
        showNotification('يرجى إدخال اسم المستخدم وكلمة المرور', 'error');
        return;
    }
    
    // Show loading
    showLoading();
    
    // Simulate API call delay
    setTimeout(() => {
        if (authenticateUser(username, password)) {
            // Save login state
            localStorage.setItem('egbank_current_user', username);
            localStorage.setItem('egbank_login_time', new Date().toISOString());
            
            // Save credentials if remember me is checked
            if (rememberMe) {
                localStorage.setItem('egbank_saved_username', username);
                localStorage.setItem('egbank_remember_me', 'true');
            } else {
                localStorage.removeItem('egbank_saved_username');
                localStorage.removeItem('egbank_remember_me');
            }
            
            showNotification('تم تسجيل الدخول بنجاح!', 'success');
            
            // Redirect after short delay
            setTimeout(() => {
                redirectToStore();
            }, 1500);
        } else {
            hideLoading();
            showNotification('اسم المستخدم أو كلمة المرور غير صحيحة', 'error');
        }
    }, 1500);
}

// Authenticate user
function authenticateUser(username, password) {
    const user = users[username];
    return user && user.password === password;
}

// Handle registration
function handleRegister(event) {
    event.preventDefault();
    
    const newUsername = document.getElementById('newUsername').value.trim();
    const email = document.getElementById('email').value.trim();
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const fullName = document.getElementById('fullName').value.trim();
    
    // Validation
    if (!newUsername || !email || !newPassword || !confirmPassword || !fullName) {
        showNotification('يرجى ملء جميع الحقول', 'error');
        return;
    }
    
    if (users[newUsername]) {
        showNotification('اسم المستخدم موجود بالفعل', 'error');
        return;
    }
    
    if (newPassword !== confirmPassword) {
        showNotification('كلمات المرور غير متطابقة', 'error');
        return;
    }
    
    if (newPassword.length < 6) {
        showNotification('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error');
        return;
    }
    
    // Create new user
    users[newUsername] = {
        password: newPassword,
        email: email,
        fullName: fullName,
        role: 'user',
        createdAt: new Date().toISOString()
    };
    
    // Save to localStorage
    localStorage.setItem('egbank_users', JSON.stringify(users));
    
    showNotification('تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول', 'success');
    
    // Close modal and fill login form
    closeRegisterModal();
    document.getElementById('username').value = newUsername;
    document.getElementById('password').focus();
}

// Handle forgot password
function handleForgotPassword(event) {
    event.preventDefault();
    
    const identifier = document.getElementById('resetIdentifier').value.trim();
    
    if (!identifier) {
        showNotification('يرجى إدخال اسم المستخدم أو البريد الإلكتروني', 'error');
        return;
    }
    
    // Check if user exists
    let userFound = false;
    for (const username in users) {
        if (username === identifier || users[username].email === identifier) {
            userFound = true;
            break;
        }
    }
    
    if (userFound) {
        showNotification('تم إرسال رابط استعادة كلمة المرور إلى بريدك الإلكتروني', 'success');
        closeForgotPasswordModal();
    } else {
        showNotification('المستخدم غير موجود', 'error');
    }
}

// Toggle password visibility
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('toggleIcon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.className = 'fas fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        toggleIcon.className = 'fas fa-eye';
    }
}

// Fill demo account credentials
function fillDemoAccount(username, password) {
    document.getElementById('username').value = username;
    document.getElementById('password').value = password;
    
    // Add visual feedback
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    
    usernameInput.style.background = '#e8f5e8';
    passwordInput.style.background = '#e8f5e8';
    
    setTimeout(() => {
        usernameInput.style.background = 'white';
        passwordInput.style.background = 'white';
    }, 1000);
    
    showNotification(`تم ملء بيانات حساب: ${username}`, 'info');
}

// Show register modal
function showRegisterForm() {
    document.getElementById('registerModal').style.display = 'block';
    document.body.style.overflow = 'hidden';
}

// Close register modal
function closeRegisterModal() {
    document.getElementById('registerModal').style.display = 'none';
    document.body.style.overflow = 'auto';
    
    // Reset form
    document.getElementById('registerForm').reset();
}

// Show forgot password modal
function showForgotPassword() {
    document.getElementById('forgotPasswordModal').style.display = 'block';
    document.body.style.overflow = 'hidden';
}

// Close forgot password modal
function closeForgotPasswordModal() {
    document.getElementById('forgotPasswordModal').style.display = 'none';
    document.body.style.overflow = 'auto';
    
    // Reset form
    document.getElementById('forgotPasswordForm').reset();
}

// Show loading overlay
function showLoading() {
    document.getElementById('loadingOverlay').style.display = 'block';
}

// Hide loading overlay
function hideLoading() {
    document.getElementById('loadingOverlay').style.display = 'none';
}

// Load saved credentials
function loadSavedCredentials() {
    const savedUsername = localStorage.getItem('egbank_saved_username');
    const rememberMe = localStorage.getItem('egbank_remember_me');
    
    if (savedUsername && rememberMe === 'true') {
        document.getElementById('username').value = savedUsername;
        document.getElementById('rememberMe').checked = true;
    }
}

// Redirect to store
function redirectToStore() {
    window.location.href = 'store.html';
}

// Logout function
function logout() {
    localStorage.removeItem('egbank_current_user');
    localStorage.removeItem('egbank_login_time');
    showNotification('تم تسجيل الخروج', 'info');
}

// Show notification
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    
    let backgroundColor;
    switch(type) {
        case 'success':
            backgroundColor = '#4CAF50';
            break;
        case 'error':
            backgroundColor = '#f44336';
            break;
        case 'warning':
            backgroundColor = '#ff9800';
            break;
        case 'info':
        default:
            backgroundColor = '#2196F3';
            break;
    }
    
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${backgroundColor};
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        z-index: 2001;
        font-weight: 600;
        animation: slideInRight 0.3s ease;
        max-width: 300px;
        word-wrap: break-word;
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(100px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    @keyframes slideOutRight {
        from {
            opacity: 1;
            transform: translateX(0);
        }
        to {
            opacity: 0;
            transform: translateX(100px);
        }
    }
`;
document.head.appendChild(style);
