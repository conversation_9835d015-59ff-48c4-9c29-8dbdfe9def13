/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Top Bar */
.top-bar {
    background: #f8f9fa;
    padding: 8px 0;
    font-size: 14px;
    border-bottom: 1px solid #e9ecef;
}

.top-bar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.top-left {
    color: #666;
}

.top-right {
    display: flex;
    gap: 20px;
}

.top-link {
    color: #666;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: color 0.3s ease;
}

.top-link:hover {
    color: #7c4dff;
}

/* Header */
.header {
    background: white;
    padding: 15px 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 30px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 24px;
    font-weight: 700;
    color: #7c4dff;
}

.logo i {
    font-size: 28px;
}

.search-bar {
    flex: 1;
    max-width: 500px;
    display: flex;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    overflow: hidden;
    transition: border-color 0.3s ease;
}

.search-bar:focus-within {
    border-color: #7c4dff;
}

.search-bar input {
    flex: 1;
    padding: 12px 20px;
    border: none;
    outline: none;
    font-size: 16px;
}

.search-btn {
    background: #7c4dff;
    color: white;
    border: none;
    padding: 12px 25px;
    cursor: pointer;
    font-weight: 600;
    transition: background 0.3s ease;
}

.search-btn:hover {
    background: #6a3de8;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

/* User Info Styles */
.user-info {
    position: relative;
}

.user-dropdown {
    position: relative;
}

.user-avatar {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 10px 15px;
    border-radius: 20px;
    transition: background 0.3s ease;
    font-weight: 500;
    color: #333;
}

.user-avatar:hover {
    background: #f8f9fa;
}

.user-avatar i:first-child {
    width: 32px;
    height: 32px;
    background: #7c4dff;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.user-avatar i:last-child {
    font-size: 12px;
    transition: transform 0.3s ease;
}

.user-dropdown:hover .user-avatar i:last-child {
    transform: rotate(180deg);
}

.user-menu {
    display: none;
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    min-width: 220px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    border-radius: 10px;
    padding: 10px 0;
    z-index: 1000;
    border: 1px solid #e9ecef;
}

.user-menu-header {
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
}

.user-menu-header strong {
    display: block;
    color: #333;
    font-size: 16px;
    margin-bottom: 5px;
}

.user-menu-header small {
    color: #666;
    font-size: 14px;
}

.user-menu-divider {
    height: 1px;
    background: #e9ecef;
    margin: 10px 0;
}

.user-menu a {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    color: #333;
    text-decoration: none;
    transition: background 0.3s ease;
    font-size: 14px;
}

.user-menu a:hover {
    background: #f8f9fa;
}

.user-menu a i {
    width: 16px;
    color: #666;
}

.logout-link {
    color: #dc3545 !important;
}

.logout-link:hover {
    background: #fff5f5 !important;
}

.logout-link i {
    color: #dc3545 !important;
}

.wishlist {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 10px 15px;
    border-radius: 20px;
    transition: background 0.3s ease;
    position: relative;
}

.wishlist:hover {
    background: #f8f9fa;
}

.badge {
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    position: absolute;
    top: 5px;
    right: 5px;
}

/* Navigation */
.navigation {
    background: #7c4dff;
    padding: 0;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-menu li {
    position: relative;
}

.nav-link {
    display: block;
    color: white;
    text-decoration: none;
    padding: 15px 20px;
    transition: background 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.nav-link:hover,
.nav-link.active {
    background: rgba(255,255,255,0.1);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    min-width: 200px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border-radius: 5px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    list-style: none;
    z-index: 1000;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu a {
    color: #333;
    padding: 12px 20px;
    display: block;
    text-decoration: none;
    transition: background 0.3s ease;
}

.dropdown-menu a:hover {
    background: #f8f9fa;
}

/* Main Content */
.main-content {
    min-height: calc(100vh - 200px);
    padding: 40px 0;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

/* Hero Banner */
.hero-banner {
    background: linear-gradient(135deg, #7c4dff, #448aff);
    color: white;
    text-align: center;
    padding: 80px 20px;
    border-radius: 15px;
    margin-bottom: 50px;
}

.hero-banner h1 {
    font-size: 48px;
    margin-bottom: 20px;
    font-weight: 700;
}

.hero-banner p {
    font-size: 20px;
    margin-bottom: 30px;
    opacity: 0.9;
}

.cta-button {
    background: white;
    color: #7c4dff;
    border: none;
    padding: 15px 40px;
    font-size: 18px;
    font-weight: 600;
    border-radius: 25px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.cta-button:hover {
    transform: translateY(-2px);
}

/* Categories */
.categories-section {
    margin-bottom: 50px;
}

.categories-section h2 {
    text-align: center;
    margin-bottom: 40px;
    font-size: 36px;
    color: #333;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.category-card {
    background: white;
    padding: 40px 20px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: transform 0.3s ease;
}

.category-card:hover {
    transform: translateY(-5px);
}

.category-card i {
    font-size: 48px;
    color: #7c4dff;
    margin-bottom: 20px;
}

.category-card h3 {
    font-size: 24px;
    margin-bottom: 10px;
    color: #333;
}

.category-card p {
    color: #666;
    font-size: 16px;
}

/* Products Section */
.products-section {
    margin-bottom: 50px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    flex-wrap: wrap;
    gap: 20px;
}

.section-header h2 {
    font-size: 36px;
    color: #333;
}

.filter-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-btn {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    color: #666;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
    background: #7c4dff;
    border-color: #7c4dff;
    color: white;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 30px;
}

.product-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    cursor: pointer;
}

.product-card:hover {
    transform: translateY(-5px);
}

.product-image {
    height: 200px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-info {
    padding: 20px;
}

.product-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
}

.product-description {
    color: #666;
    font-size: 14px;
    margin-bottom: 15px;
    line-height: 1.5;
}

.product-price {
    font-size: 20px;
    font-weight: 700;
    color: #7c4dff;
    margin-bottom: 15px;
}

.product-actions {
    display: flex;
    gap: 10px;
}

.btn-primary {
    background: #7c4dff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 600;
    flex: 1;
    transition: background 0.3s ease;
}

.btn-primary:hover {
    background: #6a3de8;
}

.btn-secondary {
    background: transparent;
    color: #7c4dff;
    border: 2px solid #7c4dff;
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    font-weight: 600;
    flex: 1;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #7c4dff;
    color: white;
}

/* Other Sections */
.post-right-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 40px;
}

.main-content-area {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.sidebar {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    height: fit-content;
}

.sidebar ul {
    list-style: none;
}

.sidebar li {
    margin-bottom: 10px;
}

.sidebar a {
    color: #666;
    text-decoration: none;
    transition: color 0.3s ease;
}

.sidebar a:hover {
    color: #7c4dff;
}

.full-width-content {
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.full-width-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.gallery-item {
    height: 150px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
}

.document-content,
.contact-content {
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.doc-section {
    margin-bottom: 30px;
}

.doc-section h3 {
    color: #7c4dff;
    margin-bottom: 15px;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
}

.contact-info h3,
.contact-form h3 {
    color: #7c4dff;
    margin-bottom: 20px;
}

.contact-info p {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.contact-form form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.contact-form input,
.contact-form textarea {
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.contact-form input:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: #7c4dff;
}

.contact-form textarea {
    min-height: 120px;
    resize: vertical;
}

.contact-form button {
    background: #7c4dff;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: background 0.3s ease;
}

.contact-form button:hover {
    background: #6a3de8;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 800px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 20px 30px;
    border-bottom: 1px solid #e9ecef;
}

.modal-body {
    padding: 30px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.close {
    position: absolute;
    top: 15px;
    left: 20px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #666;
    transition: color 0.3s ease;
}

.close:hover {
    color: #333;
}

.modal .product-image {
    height: 300px;
    border-radius: 10px;
}

/* Wishlist Item Styles */
.wishlist-item {
    transition: all 0.3s ease !important;
}

.wishlist-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.wishlist-item button {
    transition: all 0.3s ease;
}

.wishlist-item button:hover {
    transform: translateY(-1px);
}

.product-details h3 {
    font-size: 24px;
    margin-bottom: 15px;
    color: #333;
}

.product-details p {
    color: #666;
    margin-bottom: 20px;
    line-height: 1.6;
}

.product-details .product-price {
    font-size: 28px;
    margin-bottom: 25px;
}

.product-details .product-actions {
    gap: 15px;
}

/* Footer */
.footer {
    background: #2c3e50;
    color: white;
    padding: 50px 0 20px;
    margin-top: 80px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 30px;
}

.footer-section h4 {
    margin-bottom: 20px;
    color: #7c4dff;
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    margin-bottom: 8px;
}

.footer-section a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: white;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: #7c4dff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease;
}

.social-links a:hover {
    transform: translateY(-2px);
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #34495e;
    color: #bdc3c7;
}

/* Security Brands Styles */
.brands-intro {
    text-align: center;
    margin-bottom: 50px;
    padding: 0 20px;
}

.brands-intro p {
    font-size: 18px;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.brands-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 50px;
}

.brand-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.brand-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    border-color: #7c4dff;
}

.brand-logo {
    text-align: center;
    margin-bottom: 20px;
    position: relative;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.brand-logo img {
    max-height: 80px;
    max-width: 150px;
    object-fit: contain;
}

.brand-placeholder {
    display: none;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    color: #7c4dff;
}

.brand-placeholder i {
    font-size: 40px;
}

.brand-placeholder span {
    font-size: 18px;
    font-weight: 600;
}

.brand-info h3 {
    font-size: 24px;
    color: #333;
    margin-bottom: 15px;
    text-align: center;
}

.brand-info p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
    text-align: center;
}

.brand-features {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.feature-tag {
    background: linear-gradient(135deg, #7c4dff, #6a3de8);
    color: white;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
}

/* Brand Modal Styles */
.brand-modal .modal-content {
    max-width: 900px;
    width: 95%;
    max-height: 90vh;
    overflow-y: auto;
}

.brand-modal-header {
    background: linear-gradient(135deg, #7c4dff, #6a3de8);
    color: white;
    padding: 30px;
    display: flex;
    align-items: center;
    gap: 20px;
}

.brand-modal-logo {
    width: 80px;
    height: 80px;
    background: rgba(255,255,255,0.2);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
}

.brand-modal-title h2 {
    font-size: 28px;
    margin-bottom: 5px;
}

.brand-modal-title p {
    opacity: 0.9;
    font-size: 16px;
}

.brand-modal-body {
    padding: 0;
}

.brand-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.tab-btn {
    flex: 1;
    background: none;
    border: none;
    padding: 15px 20px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    color: #666;
    transition: all 0.3s ease;
}

.tab-btn.active,
.tab-btn:hover {
    background: white;
    color: #7c4dff;
    border-bottom: 3px solid #7c4dff;
}

.brand-tab-content {
    padding: 30px;
}

.tab-panel {
    display: none;
}

.tab-panel.active {
    display: block;
}

.brand-overview {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    align-items: start;
}

.overview-text {
    font-size: 16px;
    line-height: 1.8;
    color: #333;
}

.brand-stats {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 25px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
}

.stat-item:last-child {
    margin-bottom: 0;
}

.stat-item i {
    width: 40px;
    height: 40px;
    background: #7c4dff;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.stat-item div strong {
    display: block;
    font-size: 18px;
    color: #333;
    margin-bottom: 2px;
}

.stat-item div span {
    font-size: 14px;
    color: #666;
}

.brand-products {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.product-item {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
}

.product-item i {
    font-size: 32px;
    color: #7c4dff;
    margin-bottom: 15px;
}

.product-item h4 {
    color: #333;
    margin-bottom: 10px;
}

.product-item p {
    color: #666;
    font-size: 14px;
}

.brand-features-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
}

.feature-item i {
    width: 40px;
    height: 40px;
    background: #7c4dff;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    flex-shrink: 0;
}

.feature-item div h4 {
    color: #333;
    margin-bottom: 8px;
}

.feature-item div p {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
}

.brand-contact-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.contact-item {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
}

.contact-item i {
    font-size: 32px;
    color: #7c4dff;
    margin-bottom: 15px;
}

.contact-item h4 {
    color: #333;
    margin-bottom: 10px;
}

.contact-item p {
    color: #666;
}

.brand-modal-actions {
    padding: 20px 30px;
    background: #f8f9fa;
    display: flex;
    gap: 15px;
    justify-content: center;
}

.brand-modal-actions .btn-primary,
.brand-modal-actions .btn-secondary {
    min-width: 150px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 15px;
    }
    
    .nav-menu {
        flex-direction: column;
    }
    
    .hero-banner h1 {
        font-size: 32px;
    }
    
    .section-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .post-right-content,
    .contact-content {
        grid-template-columns: 1fr;
    }
    
    .modal-body {
        grid-template-columns: 1fr;
    }
    
    .categories-grid {
        grid-template-columns: 1fr;
    }

    .brands-grid {
        grid-template-columns: 1fr;
    }

    .brand-overview {
        grid-template-columns: 1fr;
    }

    .brand-tabs {
        flex-direction: column;
    }

    .tab-btn {
        text-align: center;
    }

    .brand-modal-header {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .brand-modal-actions {
        flex-direction: column;
    }

    .brand-products {
        grid-template-columns: 1fr;
    }

    .brand-features-list {
        grid-template-columns: 1fr;
    }

    .brand-contact-info {
        grid-template-columns: 1fr;
    }
}
